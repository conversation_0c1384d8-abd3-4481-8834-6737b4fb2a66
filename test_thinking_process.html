<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>思考過程測試</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    .fade-in{animation:fadeIn .5s ease-in-out}
    @keyframes fadeIn{
      from{opacity:0;transform:translateY(10px)}
      to{opacity:1;transform:translateY(0)}
    }

    /* 思考過程樣式 */
    .thinking-section {
      border: 1px solid #374151;
      border-radius: 8px;
      overflow: hidden;
      margin-bottom: 12px;
    }

    .thinking-header {
      transition: background-color 0.2s ease;
    }

    .thinking-header:hover {
      background-color: #1f2937 !important;
    }

    .thinking-content {
      max-height: 300px;
      overflow-y: auto;
      border-top: 1px solid #374151;
      font-family: 'Courier New', monospace;
      line-height: 1.4;
    }

    .thinking-toggle {
      transition: transform 0.2s ease;
    }
  </style>
</head>
<body class="bg-gray-900 text-white p-8">
  <div class="max-w-4xl mx-auto">
    <h1 class="text-2xl font-bold mb-6">🧠 思考過程顯示測試</h1>
    
    <div class="space-y-4">
      <!-- 測試按鈕 -->
      <div class="flex gap-4 mb-6">
        <button onclick="simulateThinkingResponse()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
          模擬 DeepSeek 思考過程
        </button>
        <button onclick="simulateNormalResponse()" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded">
          模擬一般回應
        </button>
        <button onclick="clearMessages()" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded">
          清除訊息
        </button>
      </div>
      
      <!-- 訊息顯示區域 -->
      <div id="messages" class="space-y-4">
        <!-- 訊息會動態添加到這裡 -->
      </div>
    </div>
  </div>

  <script>
    // 思考過程切換函數
    function toggleThinking(headerElement) {
      const thinkingSection = headerElement.parentNode;
      const content = thinkingSection.querySelector('.thinking-content');
      const toggle = thinkingSection.querySelector('.thinking-toggle');
      
      if (content.style.display === 'none') {
        content.style.display = 'block';
        toggle.textContent = '▼';
        headerElement.querySelector('span').innerHTML = '💭 思考過程 <span class="text-xs text-gray-500">(點擊收起)</span>';
      } else {
        content.style.display = 'none';
        toggle.textContent = '▶';
        headerElement.querySelector('span').innerHTML = '💭 思考過程 <span class="text-xs text-gray-500">(點擊展開)</span>';
      }
    }

    // 模擬思考過程回應
    async function simulateThinkingResponse() {
      const messagesDiv = document.getElementById('messages');
      
      // 創建 AI 回應容器
      const aiMessage = document.createElement('div');
      aiMessage.className = 'flex justify-start fade-in mb-4';
      aiMessage.innerHTML = `
        <div class="max-w-lg px-3 py-2 rounded-2xl shadow-lg bg-gray-700 text-white">
          <div class="flex items-center gap-2">
            <span class="text-xs text-blue-400 flex-shrink-0">DeepSeek</span>
            <div class="ai-response-content flex-1"></div>
          </div>
        </div>
      `;
      
      messagesDiv.appendChild(aiMessage);
      const aiBubble = aiMessage.querySelector('div.bg-gray-700');
      
      // 模擬流式回應
      const fullResponse = `<think>
用戶問的是關於高科大的問題。我需要思考一下如何回答：

1. 首先確認這是關於高雄科技大學的問題
2. 需要提供準確的資訊
3. 回答要有條理且有幫助

讓我整理一下高科大的基本資訊：
- 全名：國立高雄科技大學
- 成立時間：2018年由三校合併而成
- 校區：楠梓校區、建工校區、第一校區、燕巢校區、旗津校區
- 特色：技職教育、產學合作

這樣回答應該比較完整。
</think>

根據您的問題，我來為您介紹國立高雄科技大學：

🏫 **學校概況**
國立高雄科技大學是台灣規模最大的科技大學，於2018年由高雄第一科技大學、高雄應用科技大學及高雄海洋科技大學合併而成。

📍 **校區分布**
- 楠梓校區：主要校區，工程學院
- 建工校區：管理學院、外語學院  
- 第一校區：電資學院
- 燕巢校區：海事學院
- 旗津校區：海洋工程

🎓 **教育特色**
- 技職教育領導品牌
- 產學合作緊密
- 國際化程度高
- 就業率優異

有什麼特定方面想了解的嗎？`;

      // 模擬思考過程處理
      let thinkingContent = "";
      let isInThinking = false;
      let finalContent = "";
      let thinkingDiv = null;
      
      function createThinkingSection() {
        if (!thinkingDiv) {
          thinkingDiv = document.createElement('div');
          thinkingDiv.className = 'thinking-section mb-3';
          thinkingDiv.innerHTML = `
            <div class="thinking-header bg-gray-800 p-2 rounded-t cursor-pointer flex items-center justify-between" onclick="toggleThinking(this)">
              <span class="text-xs text-yellow-400">💭 思考過程</span>
              <span class="thinking-toggle text-xs">▼</span>
            </div>
            <div class="thinking-content bg-gray-900 p-3 rounded-b text-sm text-gray-300 whitespace-pre-wrap" style="display: none;"></div>
          `;
          aiBubble.insertBefore(thinkingDiv, aiBubble.querySelector('.ai-response-content').parentNode);
        }
        return thinkingDiv;
      }
      
      function updateThinkingContent(content) {
        const thinkingSection = createThinkingSection();
        const contentDiv = thinkingSection.querySelector('.thinking-content');
        contentDiv.textContent = content;
        contentDiv.style.display = 'block';
      }
      
      function finalizeThinking() {
        if (thinkingDiv) {
          const header = thinkingDiv.querySelector('.thinking-header');
          const toggle = thinkingDiv.querySelector('.thinking-toggle');
          const content = thinkingDiv.querySelector('.thinking-content');
          
          // 收起思考過程
          content.style.display = 'none';
          toggle.textContent = '▶';
          
          // 更新標題顯示完成狀態
          header.querySelector('span').innerHTML = '💭 思考過程 <span class="text-xs text-gray-500">(點擊展開)</span>';
        }
      }
      
      // 逐字符模擬
      for (let i = 0; i < fullResponse.length; i++) {
        const char = fullResponse[i];
        
        // 檢查 <think> 標籤
        if (fullResponse.substring(i).startsWith('<think>')) {
          isInThinking = true;
          i += 6; // 跳過 '<think>'
          continue;
        }
        
        // 檢查 </think> 標籤
        if (fullResponse.substring(i).startsWith('</think>')) {
          updateThinkingContent(thinkingContent);
          finalizeThinking();
          isInThinking = false;
          i += 7; // 跳過 '</think>'
          continue;
        }
        
        if (isInThinking) {
          thinkingContent += char;
          updateThinkingContent(thinkingContent);
        } else {
          finalContent += char;
          const contentDiv = aiBubble.querySelector('.ai-response-content');
          if (contentDiv) {
            contentDiv.textContent = finalContent;
          }
        }
        
        // 模擬打字延遲
        await new Promise(resolve => setTimeout(resolve, 20));
      }
    }

    // 模擬一般回應
    async function simulateNormalResponse() {
      const messagesDiv = document.getElementById('messages');
      
      const aiMessage = document.createElement('div');
      aiMessage.className = 'flex justify-start fade-in mb-4';
      aiMessage.innerHTML = `
        <div class="max-w-lg px-3 py-2 rounded-2xl shadow-lg bg-gray-700 text-white">
          <div class="flex items-center gap-2">
            <span class="text-xs text-blue-400 flex-shrink-0">o3-mini</span>
            <div class="ai-response-content flex-1"></div>
          </div>
        </div>
      `;
      
      messagesDiv.appendChild(aiMessage);
      const contentDiv = aiMessage.querySelector('.ai-response-content');
      
      const response = "這是一個一般的回應，沒有思考過程標籤。內容會直接顯示，不會有思考過程的展開/收起功能。";
      
      // 逐字符顯示
      for (let i = 0; i < response.length; i++) {
        contentDiv.textContent = response.substring(0, i + 1);
        await new Promise(resolve => setTimeout(resolve, 30));
      }
    }

    // 清除訊息
    function clearMessages() {
      document.getElementById('messages').innerHTML = '';
    }
  </script>
</body>
</html>
