#!/usr/bin/env python3
"""
測試 o3-mini 修復後的回應
"""

import asyncio
import aiohttp
import json

async def test_o3_mini_response():
    """測試 o3-mini 回應"""
    
    url = "http://localhost:8088/ui/chat"
    
    test_data = {
        "user_id": "test_o3_mini_fix",
        "message": "你好，請簡單介紹一下你自己，並說明你能提供什麼服務",
        "model_id": "o3-mini",
        "mode": "default"
    }
    
    print("🧪 測試 o3-mini 修復後的回應")
    print("=" * 50)
    print(f"📤 發送請求: {test_data['message']}")
    print("-" * 50)
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=test_data) as response:
                print(f"📊 狀態碼: {response.status}")
                
                if response.status == 200:
                    print("📥 回應內容:")
                    print("-" * 30)
                    
                    content_length = 0
                    response_parts = []
                    
                    async for chunk in response.content.iter_chunked(1024):
                        if chunk:
                            text = chunk.decode('utf-8')
                            content_length += len(text)
                            response_parts.append(text)
                            print(text, end='', flush=True)
                    
                    print(f"\n" + "-" * 30)
                    print(f"📏 總長度: {content_length} 字元")
                    
                    if content_length == 0:
                        print("❌ 警告: 回應仍然為空!")
                        return False
                    elif content_length < 50:
                        print("⚠️ 警告: 回應過短，可能有問題")
                        return False
                    else:
                        print("✅ o3-mini 回應正常!")
                        return True
                        
                else:
                    error_text = await response.text()
                    print(f"❌ HTTP 錯誤 {response.status}: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ 連接錯誤: {e}")
        return False

async def test_deepseek_comparison():
    """對比測試 DeepSeek 回應"""
    
    url = "http://localhost:8088/ui/chat"
    
    test_data = {
        "user_id": "test_deepseek_comparison",
        "message": "你好，請簡單介紹一下你自己",
        "model_id": "deepseek",
        "mode": "deepseek"
    }
    
    print("\n🧪 對比測試 DeepSeek 回應")
    print("=" * 50)
    print(f"📤 發送請求: {test_data['message']}")
    print("-" * 50)
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=test_data) as response:
                print(f"📊 狀態碼: {response.status}")
                
                if response.status == 200:
                    print("📥 回應內容:")
                    print("-" * 30)
                    
                    content_length = 0
                    async for chunk in response.content.iter_chunked(1024):
                        if chunk:
                            text = chunk.decode('utf-8')
                            content_length += len(text)
                            print(text, end='', flush=True)
                    
                    print(f"\n" + "-" * 30)
                    print(f"📏 總長度: {content_length} 字元")
                    print("✅ DeepSeek 回應正常!")
                    return True
                        
                else:
                    error_text = await response.text()
                    print(f"❌ HTTP 錯誤 {response.status}: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ 連接錯誤: {e}")
        return False

async def test_custom_role_with_o3():
    """測試自定義角色使用 o3-mini"""
    
    url = "http://localhost:8088/ui/chat"
    
    test_data = {
        "user_id": "test_custom_o3",
        "message": "你好，請介紹一下你的服務",
        "model_id": "o3-mini",
        "mode": "custom_57121e52"  # 使用之前創建的學術顧問角色
    }
    
    print("\n🧪 測試自定義角色 + o3-mini")
    print("=" * 50)
    print(f"📤 使用角色: custom_57121e52 (學術顧問)")
    print(f"📤 發送請求: {test_data['message']}")
    print("-" * 50)
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=test_data) as response:
                print(f"📊 狀態碼: {response.status}")
                
                if response.status == 200:
                    print("📥 回應內容:")
                    print("-" * 30)
                    
                    content_length = 0
                    async for chunk in response.content.iter_chunked(1024):
                        if chunk:
                            text = chunk.decode('utf-8')
                            content_length += len(text)
                            print(text, end='', flush=True)
                    
                    print(f"\n" + "-" * 30)
                    print(f"📏 總長度: {content_length} 字元")
                    
                    if content_length > 0:
                        print("✅ 自定義角色 + o3-mini 回應正常!")
                        return True
                    else:
                        print("❌ 自定義角色 + o3-mini 回應為空!")
                        return False
                        
                else:
                    error_text = await response.text()
                    print(f"❌ HTTP 錯誤 {response.status}: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ 連接錯誤: {e}")
        return False

async def main():
    """主測試函數"""
    print("🚀 測試 o3-mini 修復")
    print("=" * 60)
    
    # 測試結果
    results = []
    
    # 1. 測試 o3-mini 基本回應
    o3_result = await test_o3_mini_response()
    results.append(("o3-mini 基本回應", o3_result))
    
    # 2. 對比測試 DeepSeek
    deepseek_result = await test_deepseek_comparison()
    results.append(("DeepSeek 對比", deepseek_result))
    
    # 3. 測試自定義角色 + o3-mini
    custom_result = await test_custom_role_with_o3()
    results.append(("自定義角色 + o3-mini", custom_result))
    
    # 總結
    print("\n🎯 測試總結")
    print("=" * 60)
    
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    print(f"\n📊 總體結果: {success_count}/{len(results)} 個測試通過")
    
    if success_count == len(results):
        print("🎉 所有測試通過！o3-mini 問題已修復")
    else:
        print("⚠️ 仍有問題需要進一步調查")

if __name__ == "__main__":
    asyncio.run(main())
