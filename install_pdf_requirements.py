#!/usr/bin/env python3
"""
安裝 PDF 處理所需的套件
"""

import subprocess
import sys
import importlib

def install_package(package_name):
    """安裝指定的套件"""
    try:
        print(f"📦 正在安裝 {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, check=True)
        
        print(f"✅ {package_name} 安裝成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安裝失敗:")
        print(f"   錯誤: {e.stderr}")
        return False

def check_package(package_name):
    """檢查套件是否已安裝"""
    try:
        importlib.import_module(package_name)
        print(f"✅ {package_name} 已安裝")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安裝")
        return False

def main():
    """主安裝函數"""
    print("🚀 PDF 處理套件安裝程序")
    print("=" * 50)
    
    # PDF 處理套件列表（按優先級排序）
    pdf_packages = [
        ("pdfplumber", "pdfplumber"),  # (pip_name, import_name)
        ("PyPDF2", "PyPDF2"),
    ]
    
    # 其他相關套件
    other_packages = [
        ("python-multipart", "multipart"),  # FastAPI 檔案上傳支援
        ("aiofiles", "aiofiles"),           # 異步檔案處理
    ]
    
    print("📋 檢查現有套件...")
    
    # 檢查 PDF 處理套件
    pdf_available = False
    for pip_name, import_name in pdf_packages:
        if check_package(import_name):
            pdf_available = True
            break
    
    # 檢查其他套件
    other_available = []
    for pip_name, import_name in other_packages:
        available = check_package(import_name)
        other_available.append((pip_name, import_name, available))
    
    print("\n📦 開始安裝缺失的套件...")
    
    # 安裝 PDF 處理套件（如果都沒有的話）
    if not pdf_available:
        print("\n🔍 安裝 PDF 處理套件...")
        
        # 優先安裝 pdfplumber（功能更強大）
        success = install_package("pdfplumber")
        
        if not success:
            print("⚠️ pdfplumber 安裝失敗，嘗試安裝 PyPDF2...")
            install_package("PyPDF2")
    
    # 安裝其他必要套件
    print("\n🔧 安裝其他必要套件...")
    for pip_name, import_name, available in other_available:
        if not available:
            install_package(pip_name)
    
    print("\n🧪 驗證安裝結果...")
    
    # 最終檢查
    print("\n📊 最終檢查結果:")
    print("-" * 30)
    
    # 檢查 PDF 套件
    pdf_final = False
    for pip_name, import_name in pdf_packages:
        if check_package(import_name):
            pdf_final = True
            print(f"   📄 PDF 處理: {import_name} ✅")
            break
    
    if not pdf_final:
        print("   📄 PDF 處理: 無可用套件 ❌")
    
    # 檢查其他套件
    for pip_name, import_name, _ in other_available:
        available = check_package(import_name)
        status = "✅" if available else "❌"
        print(f"   🔧 {import_name}: {status}")
    
    print("-" * 30)
    
    if pdf_final:
        print("\n🎉 安裝完成！PDF 功能已就緒")
        print("\n💡 現在您可以：")
        print("   1. 啟動 web 應用程序")
        print("   2. 在前端上傳 PDF 檔案")
        print("   3. 讓 AI 分析 PDF 內容")
        
        print("\n🧪 測試建議：")
        print("   python test_pdf_backend.py")
        
    else:
        print("\n❌ PDF 套件安裝失敗")
        print("請手動安裝：")
        print("   pip install pdfplumber")
        print("   或")
        print("   pip install PyPDF2")
    
    print("\n📚 套件說明：")
    print("   • pdfplumber: 功能強大，支援表格和複雜佈局")
    print("   • PyPDF2: 輕量級，基本文字提取")
    print("   • python-multipart: FastAPI 檔案上傳支援")
    print("   • aiofiles: 異步檔案處理（可選）")

if __name__ == "__main__":
    main()
