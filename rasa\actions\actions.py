# This files contains your custom actions which can be used to run
# custom Python code.
#
# See this guide on how to implement these action:
# https://rasa.com/docs/rasa/custom-actions


# This is a simple example for a custom action which utters "Hello World!"

# from typing import Any, Text, Dict, List
#
# from rasa_sdk import Action, Tracker
# from rasa_sdk.executor import CollectingDispatcher
#
#
# class ActionHelloWorld(Action):
#
#     def name(self) -> Text:
#         return "action_hello_world"
#
#     def run(self, dispatcher: CollectingDispatcher,
#             tracker: Tracker,
#             domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
#
#         dispatcher.utter_message(text="Hello World!")
#
#         return []


from rasa_sdk import Action
from rasa_sdk.executor import CollectingDispatcher
import requests  # 你也可以用 aiohttp / httpx

class ActionFallbackHandle(Action):
    def name(self):
        return "action_fallback_handle"

    def run(self, dispatcher, tracker, domain):
        user_msg = tracker.latest_message.get("text")
        
        # 呼叫後端 agent（例如 FastAPI, Flask 等）
        try:
            response = requests.post("https://b225.54ucl.com/ui/chat", json={"query": user_msg})
            result = response.json().get("response", "很抱歉，我沒能理解您的問題。")
        except Exception as e:
            result = "後端處理發生錯誤：" + str(e)
        
        dispatcher.utter_message(text=result)
        return []