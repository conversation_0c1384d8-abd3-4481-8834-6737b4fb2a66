#!/usr/bin/env python3
"""
測試 ChatCompletionAgent 導入修復
"""

def test_imports():
    """測試導入是否正常"""
    print("🔧 測試導入修復")
    print("=" * 50)
    
    try:
        # 測試 agent_factory 導入
        print("📦 測試 agent_factory 導入...")
        from agent_factory import build_agent, get_instructions_for_model
        print("✅ agent_factory 導入成功")
        
        # 測試 ChatCompletionAgent 導入
        print("📦 測試 ChatCompletionAgent 導入...")
        try:
            from semantic_kernel.agents import ChatCompletionAgent
            print("✅ ChatCompletionAgent 導入成功")
            return True
        except ImportError as e:
            print(f"❌ ChatCompletionAgent 導入失敗: {e}")
            try:
                from semantic_kernel.agents.chat_completion_agent import ChatCompletionAgent
                print("✅ ChatCompletionAgent 從備用路徑導入成功")
                return True
            except ImportError as e2:
                print(f"❌ ChatCompletionAgent 備用路徑也失敗: {e2}")
                return False
                
    except Exception as e:
        print(f"❌ 導入測試失敗: {e}")
        return False

def test_web_app_syntax():
    """測試 web_app.py 語法是否正確"""
    print("\n🔧 測試 web_app.py 語法")
    print("=" * 50)
    
    try:
        import ast
        with open('web_app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析語法
        ast.parse(content)
        print("✅ web_app.py 語法正確")
        return True
        
    except SyntaxError as e:
        print(f"❌ web_app.py 語法錯誤: {e}")
        print(f"   行號: {e.lineno}, 位置: {e.offset}")
        return False
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 導入修復測試")
    print("=" * 60)
    
    # 測試導入
    import_ok = test_imports()
    
    # 測試語法
    syntax_ok = test_web_app_syntax()
    
    print("\n🎯 測試總結")
    print("=" * 60)
    print(f"導入測試: {'✅ 通過' if import_ok else '❌ 失敗'}")
    print(f"語法測試: {'✅ 通過' if syntax_ok else '❌ 失敗'}")
    
    if import_ok and syntax_ok:
        print("🎉 所有測試通過！可以啟動 web_app.py")
    else:
        print("⚠️ 仍有問題需要修復")

if __name__ == "__main__":
    main()
