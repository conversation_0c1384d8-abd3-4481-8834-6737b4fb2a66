<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDF 上傳功能測試</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white p-8">
  <div class="max-w-4xl mx-auto">
    <h1 class="text-3xl font-bold mb-8">📄 PDF 上傳功能測試</h1>
    
    <!-- PDF 上傳區域 -->
    <div id="pdf-upload-area" class="mb-4">
      <div class="bg-gray-800 border-2 border-dashed border-gray-600 rounded-xl p-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="text-red-400 text-2xl">📄</div>
            <div>
              <div class="text-sm font-medium text-white" id="pdf-filename">未選擇檔案</div>
              <div class="text-xs text-gray-400">支援 PDF 格式，最大 10MB</div>
            </div>
          </div>
          <div class="flex gap-2">
            <button onclick="selectPDF()" class="text-xs bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg transition-colors">
              選擇檔案
            </button>
            <button onclick="removePDF()" class="text-xs bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg transition-colors">
              移除
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 輸入區域 -->
    <div class="flex gap-4 mb-6">
      <div class="flex-1 relative">
        <textarea id="question"
          rows="3"
          placeholder="請輸入關於 PDF 的問題..."
          class="w-full p-4 pr-12 rounded-xl bg-gray-800 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 resize-none border border-gray-600 transition-all"></textarea>
        
        <!-- PDF 附件按鈕 -->
        <button
          onclick="togglePDFUpload()"
          class="absolute right-3 top-4 text-gray-400 hover:text-white transition-colors text-xl"
          title="上傳 PDF 檔案"
          id="pdf-attach-btn"
        >
          📎
        </button>
      </div>

      <button onclick="testSend()" class="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white font-bold px-8 py-4 rounded-xl transition-all transform hover:scale-105 shadow-lg min-w-[120px] flex items-center justify-center">
        測試發送
      </button>
    </div>
    
    <!-- 測試結果顯示 -->
    <div id="test-results" class="bg-gray-800 rounded-xl p-4 min-h-[200px]">
      <h3 class="text-lg font-semibold mb-4">測試結果：</h3>
      <div id="result-content" class="text-gray-300">
        點擊「測試發送」來測試 PDF 上傳功能
      </div>
    </div>
    
    <!-- 隱藏的檔案輸入 -->
    <input type="file" id="pdf-input" accept=".pdf" style="display: none;" onchange="handlePDFSelect(event)">
    
    <!-- 功能說明 -->
    <div class="mt-8 bg-blue-900/30 border border-blue-700 rounded-xl p-4">
      <h3 class="text-lg font-semibold mb-2">🔧 功能說明</h3>
      <ul class="text-sm text-gray-300 space-y-1">
        <li>• 點擊 📎 按鈕或「選擇檔案」來上傳 PDF</li>
        <li>• 支援最大 10MB 的 PDF 檔案</li>
        <li>• 可以只上傳 PDF 不輸入文字</li>
        <li>• 也可以同時上傳 PDF 和輸入問題</li>
        <li>• 發送後會自動清理已選擇的檔案</li>
      </ul>
    </div>
  </div>

  <script>
    // PDF 處理函數
    let selectedPDF = null;

    function togglePDFUpload() {
      const uploadArea = document.getElementById('pdf-upload-area');
      const attachBtn = document.getElementById('pdf-attach-btn');
      
      if (uploadArea.style.display === 'none') {
        uploadArea.style.display = 'block';
        attachBtn.style.color = '#3b82f6';
      } else {
        uploadArea.style.display = 'none';
        attachBtn.style.color = '';
      }
    }

    function selectPDF() {
      document.getElementById('pdf-input').click();
    }

    function handlePDFSelect(event) {
      const file = event.target.files[0];
      if (!file) return;
      
      // 檢查檔案類型
      if (file.type !== 'application/pdf') {
        alert('請選擇 PDF 檔案');
        return;
      }
      
      // 檢查檔案大小 (10MB)
      if (file.size > 10 * 1024 * 1024) {
        alert('檔案大小不能超過 10MB');
        return;
      }
      
      selectedPDF = file;
      document.getElementById('pdf-filename').textContent = file.name;
      document.getElementById('pdf-attach-btn').style.color = '#10b981';
      
      updateResults(`✅ PDF 檔案已選擇: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`);
    }

    function removePDF() {
      selectedPDF = null;
      document.getElementById('pdf-filename').textContent = '未選擇檔案';
      document.getElementById('pdf-input').value = '';
      document.getElementById('pdf-attach-btn').style.color = '';
      
      updateResults('📄 PDF 檔案已移除');
    }

    function updateResults(message) {
      const resultContent = document.getElementById('result-content');
      const timestamp = new Date().toLocaleTimeString();
      resultContent.innerHTML += `<div class="mb-2">[${timestamp}] ${message}</div>`;
      resultContent.scrollTop = resultContent.scrollHeight;
    }

    function testSend() {
      const message = document.getElementById('question').value.trim();
      
      if (!message && !selectedPDF) {
        updateResults('❌ 請輸入訊息或選擇 PDF 檔案');
        return;
      }
      
      updateResults('🚀 開始測試發送...');
      
      // 模擬發送邏輯
      let userMessage = message || "";
      if (selectedPDF) {
        userMessage += (userMessage ? "\n\n" : "") + `📄 附件: ${selectedPDF.name} (${(selectedPDF.size / 1024 / 1024).toFixed(2)}MB)`;
      }
      
      updateResults(`👤 用戶訊息: ${userMessage}`);
      
      // 模擬 FormData 創建
      if (selectedPDF) {
        const formData = new FormData();
        formData.append('user_id', 'test-user-id');
        formData.append('message', message || '請分析這個 PDF 檔案');
        formData.append('model_id', 'test-model');
        formData.append('mode', 'test-mode');
        formData.append('return_search_results', 'true');
        formData.append('pdf_file', selectedPDF);
        
        updateResults('📤 FormData 已創建，包含 PDF 檔案');
        updateResults(`📋 請求將發送到: /ui/chat_pdf`);
      } else {
        const requestData = {
          user_id: 'test-user-id',
          message: message,
          model_id: 'test-model',
          mode: 'test-mode',
          return_search_results: true
        };
        
        updateResults('📤 JSON 請求已創建');
        updateResults(`📋 請求將發送到: /ui/chat`);
        updateResults(`📄 請求內容: ${JSON.stringify(requestData, null, 2)}`);
      }
      
      // 清理
      document.getElementById('question').value = '';
      if (selectedPDF) {
        updateResults('🧹 清理 PDF 檔案...');
        removePDF();
      }
      
      updateResults('✅ 測試完成！');
    }
  </script>
</body>
</html>
