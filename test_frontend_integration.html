<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>測試自定義角色創建</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">🧪 自定義角色創建測試</h1>
        
        <!-- 測試按鈕區域 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <button onclick="testGetRoles()" class="bg-blue-600 hover:bg-blue-700 p-4 rounded-lg">
                📋 獲取角色列表
            </button>
            <button onclick="testCreateRole()" class="bg-green-600 hover:bg-green-700 p-4 rounded-lg">
                ✨ 創建測試角色
            </button>
            <button onclick="testChatWithCustomRole()" class="bg-purple-600 hover:bg-purple-700 p-4 rounded-lg">
                💬 測試對話
            </button>
        </div>
        
        <!-- 結果顯示區域 -->
        <div id="results" class="bg-gray-800 rounded-lg p-6 min-h-[400px]">
            <h2 class="text-xl font-semibold mb-4">測試結果</h2>
            <div id="output" class="text-gray-300">
                點擊上方按鈕開始測試...
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `<div class="mb-2">[${timestamp}] ${message}</div>`;
            output.scrollTop = output.scrollHeight;
        }

        function clearLog() {
            document.getElementById('output').innerHTML = '';
        }

        async function testGetRoles() {
            clearLog();
            log('🔄 正在獲取角色列表...');
            
            try {
                const response = await fetch('/ui/roles');
                const roles = await response.json();
                
                log(`✅ 成功獲取 ${Object.keys(roles).length} 個角色:`);
                
                Object.entries(roles).forEach(([roleId, config]) => {
                    const displayName = config.display_name || `${config.organization_name} - ${config.role_title}`;
                    log(`  - <span class="text-blue-400">${roleId}</span>: ${displayName}`);
                });
                
            } catch (error) {
                log(`❌ 錯誤: ${error.message}`);
            }
        }

        async function testCreateRole() {
            clearLog();
            log('🔄 正在創建測試角色...');
            
            const testRole = {
                role_name: "測試顧問",
                organization_name: "測試科技公司",
                organization_domain: "test-tech.com",
                tone_style: "專業友善",
                special_requirements: "專門處理技術諮詢和產品支援",
                description: "這是一個測試角色，用於驗證系統功能"
            };
            
            log(`📋 角色資訊: ${JSON.stringify(testRole, null, 2)}`);
            
            try {
                const response = await fetch('/ui/create-role', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testRole)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    log(`✅ 角色創建成功!`);
                    log(`  - 角色 ID: <span class="text-green-400">${result.role_id}</span>`);
                    log(`  - 最終分數: <span class="text-yellow-400">${result.final_score}/10</span>`);
                    log(`  - 迭代次數: ${result.iterations}`);
                    
                    log('📝 創建過程:');
                    result.process_log.forEach(logEntry => {
                        log(`  ${logEntry}`);
                    });
                    
                } else {
                    log(`❌ 角色創建失敗: ${result.error}`);
                }
                
            } catch (error) {
                log(`❌ 網路錯誤: ${error.message}`);
            }
        }

        async function testChatWithCustomRole() {
            clearLog();
            log('🔄 正在測試與自定義角色對話...');
            
            // 首先獲取可用的自定義角色
            try {
                const rolesResponse = await fetch('/ui/roles');
                const roles = await response.json();
                
                const customRoles = Object.keys(roles).filter(id => id.startsWith('custom_'));
                
                if (customRoles.length === 0) {
                    log('❌ 沒有找到自定義角色，請先創建一個角色');
                    return;
                }
                
                const roleId = customRoles[0];
                const roleName = roles[roleId].display_name;
                
                log(`💬 使用角色: <span class="text-purple-400">${roleName}</span>`);
                
                const chatRequest = {
                    user_id: "test_user_" + Date.now(),
                    message: "你好，請介紹一下你的服務",
                    model_id: "o3-mini",
                    mode: roleId
                };
                
                log('📤 發送對話請求...');
                
                const chatResponse = await fetch('/ui/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(chatRequest)
                });
                
                if (chatResponse.ok) {
                    log('📥 收到回應:');
                    
                    const reader = chatResponse.body.getReader();
                    const decoder = new TextDecoder();
                    let fullResponse = '';
                    
                    while (true) {
                        const { value, done } = await reader.read();
                        if (done) break;
                        
                        const chunk = decoder.decode(value, { stream: true });
                        fullResponse += chunk;
                    }
                    
                    log(`<div class="bg-gray-700 p-3 rounded mt-2 whitespace-pre-wrap">${fullResponse}</div>`);
                    
                } else {
                    log(`❌ 對話失敗: ${chatResponse.status}`);
                }
                
            } catch (error) {
                log(`❌ 錯誤: ${error.message}`);
            }
        }

        // 頁面載入時自動獲取角色列表
        window.addEventListener('DOMContentLoaded', () => {
            log('🚀 頁面載入完成，準備測試...');
        });
    </script>
</body>
</html>
