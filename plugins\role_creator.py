"""
角色創建協調器 - 協調 Prompt 生成和評分 Agent 的工作流程
"""

import json
import os
import uuid
from typing import Dict, List, Any, Tuple
from loguru import logger

from .prompt_generator import PromptGenerator
from .prompt_evaluator import PromptEvaluator
from .learning_history import LearningHistory

class RoleCreator:
    """協調角色創建流程的主要類別"""
    
    def __init__(self, kernel=None):
        self.kernel = kernel
        self.generator = PromptGenerator(kernel)
        self.evaluator = PromptEvaluator(kernel)
        self.learning_history = LearningHistory()
        self.config_path = "plugins/organization_configs.json"
        self.min_score = 8.0  # 最低接受分數
        self.max_iterations = 3  # 最大迭代次數
        
    async def create_custom_role(self, user_input: Dict[str, str]) -> Dict[str, Any]:
        """創建自定義角色的主要流程
        
        Args:
            user_input: 用戶輸入的角色資訊
            
        Returns:
            Dict 包含創建結果和過程資訊
        """
        logger.info(f"開始創建自定義角色: {user_input.get('role_name', '未命名')}")
        
        result = {
            "success": False,
            "role_id": None,
            "config": None,
            "final_score": 0.0,
            "iterations": 0,
            "process_log": [],
            "error": None
        }
        
        try:
            # 生成唯一的角色 ID
            role_id = f"custom_{uuid.uuid4().hex[:8]}"
            result["role_id"] = role_id
            
            # 迭代改進流程
            current_config = None
            current_system_prompt = None

            for iteration in range(1, self.max_iterations + 1):
                result["iterations"] = iteration

                logger.info(f"第 {iteration} 次迭代開始")
                result["process_log"].append(f"第 {iteration} 次迭代開始")

                # 生成配置和 system prompt
                if iteration == 1:
                    current_config = await self.generator.generate_config(user_input)
                    current_system_prompt = current_config.get("system_prompt", "")
                    result["process_log"].append("生成初始 system prompt")
                else:
                    # 使用改進建議重新生成
                    current_system_prompt = await self._improve_system_prompt(current_system_prompt, last_suggestions, user_input)
                    current_config["system_prompt"] = current_system_prompt
                    result["process_log"].append(f"根據建議改進 system prompt")

                # 評估 system prompt
                score, suggestions = self.evaluator.evaluate_system_prompt(current_system_prompt, user_input)
                result["final_score"] = score
                
                logger.info(f"第 {iteration} 次評估分數: {score}/10")
                result["process_log"].append(f"評估分數: {score}/10")
                
                if suggestions:
                    result["process_log"].append(f"改進建議: {len(suggestions)} 項")
                    for suggestion in suggestions[:3]:  # 只記錄前3個建議
                        result["process_log"].append(f"  - {suggestion}")
                
                # 檢查是否達到要求
                if score >= self.min_score:
                    logger.info(f"配置品質達標 (分數: {score})")
                    result["process_log"].append("✅ 配置品質達標")
                    break
                elif iteration == self.max_iterations:
                    logger.warning(f"達到最大迭代次數，使用當前最佳配置 (分數: {score})")
                    result["process_log"].append("⚠️ 達到最大迭代次數，使用當前配置")
                    break
                else:
                    last_suggestions = suggestions
                    result["process_log"].append(f"繼續改進 (目標分數: {self.min_score})")
            
            # 儲存配置
            if current_config:
                success = await self._save_config(role_id, current_config)
                if success:
                    result["success"] = True
                    result["config"] = current_config
                    result["process_log"].append("✅ 配置已儲存")

                    # 記錄學習歷史
                    self.learning_history.record_role_creation(
                        role_id, user_input, result["final_score"],
                        result["iterations"], result["process_log"]
                    )
                    result["process_log"].append("📚 學習歷史已更新")

                    logger.info(f"角色 {role_id} 創建成功")
                else:
                    result["error"] = "儲存配置失敗"
                    result["process_log"].append("❌ 儲存配置失敗")
            else:
                result["error"] = "無法生成有效配置"
                
        except Exception as e:
            logger.error(f"創建角色時發生錯誤: {e}")
            result["error"] = str(e)
            result["process_log"].append(f"❌ 錯誤: {str(e)}")
        
        return result

    async def _improve_system_prompt(self, system_prompt: str, suggestions: List[str], user_input: Dict[str, str]) -> str:
        """根據建議改進 system prompt"""
        try:
            improved_prompt = system_prompt

            # 根據建議進行基本改進
            for suggestion in suggestions:
                if "角色" in suggestion and "未明確提及" in suggestion:
                    role_name = user_input.get('role_name', '')
                    org_name = user_input.get('organization_name', '')
                    if role_name and role_name not in improved_prompt:
                        # 在開頭加強角色身份
                        improved_prompt = improved_prompt.replace(
                            f"你是{org_name}的",
                            f"你是{org_name}的{role_name}，專業的{role_name}，"
                        )

                elif "組織網域" in suggestion:
                    domain = user_input.get('organization_domain', '')
                    if domain and domain not in improved_prompt:
                        # 在網址選擇原則中加入網域
                        improved_prompt = improved_prompt.replace(
                            "- 優先選擇",
                            f"- 優先選擇 {domain} 網域的內容\n   - 優先選擇"
                        )

                elif "語調風格" in suggestion:
                    tone = user_input.get('tone_style', '')
                    if tone and tone not in improved_prompt:
                        # 在回覆要求中加入語調
                        improved_prompt = improved_prompt.replace(
                            "用繁體中文且語氣",
                            f"用繁體中文且語氣{tone}"
                        )

                elif "特殊要求" in suggestion:
                    special_req = user_input.get('special_requirements', '')
                    if special_req and len(special_req) > 10:
                        # 在規則中添加特殊要求
                        if "特殊要求：" not in improved_prompt:
                            improved_prompt += f"\n   12. 特殊要求：{special_req}"

                elif "步驟結構" in suggestion:
                    # 確保有清晰的步驟編號
                    if "1. **首先**" not in improved_prompt:
                        improved_prompt = improved_prompt.replace(
                            "**首先**",
                            "1. **首先**"
                        )

                elif "格式化元素" in suggestion:
                    # 添加更多格式化
                    improved_prompt = improved_prompt.replace(
                        "網址選擇原則",
                        "**網址選擇原則**"
                    )
                    improved_prompt = improved_prompt.replace(
                        "智能搜尋流程",
                        "**智能搜尋流程**"
                    )

            return improved_prompt

        except Exception as e:
            logger.error(f"改進 system prompt 時發生錯誤: {e}")
            return system_prompt

    async def _improve_config(self, config: Dict[str, Any], suggestions: List[str], user_input: Dict[str, str]) -> Dict[str, Any]:
        """根據建議改進配置"""
        try:
            # 這裡應該使用 AI 來改進配置
            # 暫時實現基本的改進邏輯
            
            improved_config = config.copy()
            
            # 根據建議進行基本改進
            for suggestion in suggestions:
                if "special_rules" in suggestion and "數量不足" in suggestion:
                    # 增加更多規則
                    current_rules = improved_config.get('special_rules', [])
                    additional_rules = [
                        "始終保持專業和禮貌的態度",
                        "在不確定時主動尋求澄清",
                        "提供具體且可行的建議",
                        "尊重用戶的隱私和機密資訊"
                    ]
                    
                    for rule in additional_rules:
                        if rule not in current_rules and len(current_rules) < 8:
                            current_rules.append(rule)
                    
                    improved_config['special_rules'] = current_rules
                
                elif "角色身份" in suggestion:
                    # 在規則中添加角色身份
                    role_title = improved_config.get('role_title', '')
                    org_name = improved_config.get('organization_name', '')
                    
                    if role_title and org_name:
                        identity_rule = f"你是{org_name}的{role_title}，請始終保持這個身份"
                        rules = improved_config.get('special_rules', [])
                        if identity_rule not in rules:
                            rules.insert(0, identity_rule)
                            improved_config['special_rules'] = rules
                
                elif "工具" in suggestion and "時間" in suggestion:
                    # 啟用時間工具
                    tools = improved_config.get('tools', {})
                    tools['time_required'] = True
                    improved_config['tools'] = tools
                
                elif "搜尋" in suggestion:
                    # 啟用搜尋功能
                    tools = improved_config.get('tools', {})
                    tools['search_enabled'] = True
                    improved_config['tools'] = tools
            
            return improved_config
            
        except Exception as e:
            logger.error(f"改進配置時發生錯誤: {e}")
            return config
    
    async def _save_config(self, role_id: str, config: Dict[str, Any]) -> bool:
        """儲存配置到 organization_configs.json"""
        try:
            # 載入現有配置
            existing_configs = {}
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    existing_configs = json.load(f)
            
            # 添加新配置
            existing_configs[role_id] = config
            
            # 儲存回檔案
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(existing_configs, f, ensure_ascii=False, indent=2)
            
            logger.info(f"配置 {role_id} 已儲存到 {self.config_path}")
            return True
            
        except Exception as e:
            logger.error(f"儲存配置失敗: {e}")
            return False
    
    def get_available_roles(self) -> Dict[str, Dict[str, Any]]:
        """獲取所有可用的角色配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    configs = json.load(f)
                
                # 為每個配置添加顯示名稱
                for role_id, config in configs.items():
                    if 'display_name' not in config:
                        org_name = config.get('organization_name', '組織')
                        role_title = config.get('role_title', '助理')
                        config['display_name'] = f"{org_name} - {role_title}"
                
                return configs
            return {}
        except Exception as e:
            logger.error(f"載入角色配置失敗: {e}")
            return {}
    
    def delete_role(self, role_id: str) -> bool:
        """刪除指定的角色配置"""
        try:
            if not os.path.exists(self.config_path):
                return False
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                configs = json.load(f)
            
            if role_id in configs:
                del configs[role_id]
                
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    json.dump(configs, f, ensure_ascii=False, indent=2)
                
                logger.info(f"角色 {role_id} 已刪除")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"刪除角色失敗: {e}")
            return False

    def get_learning_insights(self) -> Dict[str, Any]:
        """獲取系統學習洞察"""
        return self.learning_history.get_learning_insights()

    def get_evolution_summary(self) -> str:
        """獲取進化總結，用於改進 prompt 生成"""
        insights = self.get_learning_insights()

        summary_parts = []

        if insights["total_roles"] > 0:
            summary_parts.append(f"系統已創建 {insights['total_roles']} 個角色")

        if insights["recent_average_score"]:
            summary_parts.append(f"最近平均分數: {insights['recent_average_score']}/10")

        if insights["most_successful_tone"]:
            tone_info = insights["most_successful_tone"]
            summary_parts.append(f"最成功的語調: {tone_info['tone']} (平均分數: {tone_info['avg_score']})")

        if insights["most_common_org_type"]:
            org_info = insights["most_common_org_type"]
            summary_parts.append(f"最常見組織類型: {org_info['type']} (共 {org_info['count']} 個)")

        if insights["improvement_trend"]:
            summary_parts.append(f"改進趨勢: {insights['improvement_trend']}")

        return "; ".join(summary_parts) if summary_parts else "系統剛開始學習"
