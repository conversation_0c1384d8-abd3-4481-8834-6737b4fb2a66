"""
學習歷史記錄 - 追蹤系統的自我進化過程
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any
from loguru import logger

class LearningHistory:
    """追蹤和記錄系統學習進化的歷史"""
    
    def __init__(self):
        self.history_path = "plugins/learning_history.json"
        self.ensure_history_file()
    
    def ensure_history_file(self):
        """確保歷史記錄文件存在"""
        if not os.path.exists(self.history_path):
            initial_data = {
                "creation_date": datetime.now().isoformat(),
                "total_roles_created": 0,
                "learning_milestones": [],
                "pattern_evolution": {},
                "quality_trends": [],
                "successful_patterns": []
            }
            self.save_history(initial_data)
    
    def load_history(self) -> Dict[str, Any]:
        """載入學習歷史"""
        try:
            with open(self.history_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"載入學習歷史失敗: {e}")
            return {}
    
    def save_history(self, history: Dict[str, Any]):
        """儲存學習歷史"""
        try:
            with open(self.history_path, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"儲存學習歷史失敗: {e}")
    
    def record_role_creation(self, role_id: str, user_input: Dict[str, str], 
                           final_score: float, iterations: int, 
                           process_log: List[str]):
        """記錄角色創建事件"""
        history = self.load_history()
        
        # 更新總計數
        history["total_roles_created"] = history.get("total_roles_created", 0) + 1
        
        # 記錄創建事件
        creation_record = {
            "timestamp": datetime.now().isoformat(),
            "role_id": role_id,
            "organization_type": self._classify_organization(user_input.get('organization_name', '')),
            "role_type": user_input.get('role_name', ''),
            "tone_style": user_input.get('tone_style', ''),
            "final_score": final_score,
            "iterations": iterations,
            "success": final_score >= 8.0,
            "process_summary": len(process_log)
        }
        
        # 添加到品質趨勢
        if "quality_trends" not in history:
            history["quality_trends"] = []
        history["quality_trends"].append({
            "timestamp": datetime.now().isoformat(),
            "score": final_score,
            "iterations": iterations,
            "role_count": history["total_roles_created"]
        })
        
        # 記錄學習里程碑
        self._check_milestones(history, creation_record)
        
        # 更新模式進化
        self._update_pattern_evolution(history, user_input, final_score)
        
        # 記錄成功模式
        if final_score >= 9.0:
            self._record_successful_pattern(history, user_input, final_score)
        
        self.save_history(history)
        logger.info(f"記錄角色創建歷史: {role_id}, 分數: {final_score}")
    
    def _classify_organization(self, org_name: str) -> str:
        """分類組織類型"""
        org_name_lower = org_name.lower()
        if any(keyword in org_name_lower for keyword in ['大學', '學院', '學校']):
            return '教育機構'
        elif any(keyword in org_name_lower for keyword in ['公司', '企業', '科技']):
            return '企業'
        elif any(keyword in org_name_lower for keyword in ['醫院', '診所', '醫療']):
            return '醫療機構'
        elif any(keyword in org_name_lower for keyword in ['政府', '機關', '部門']):
            return '政府機構'
        else:
            return '其他'
    
    def _check_milestones(self, history: Dict[str, Any], creation_record: Dict[str, Any]):
        """檢查並記錄學習里程碑"""
        milestones = history.get("learning_milestones", [])
        total_roles = history.get("total_roles_created", 0)
        
        # 里程碑檢查
        milestone_checks = [
            (1, "🎉 創建了第一個自定義角色"),
            (5, "🚀 創建了 5 個角色，開始積累經驗"),
            (10, "💪 創建了 10 個角色，系統日趨成熟"),
            (25, "🏆 創建了 25 個角色，成為專家級系統"),
            (50, "🌟 創建了 50 個角色，達到大師級水準")
        ]
        
        for count, message in milestone_checks:
            if total_roles == count:
                milestone = {
                    "timestamp": datetime.now().isoformat(),
                    "role_count": count,
                    "message": message,
                    "average_score": self._calculate_recent_average_score(history)
                }
                milestones.append(milestone)
                logger.info(f"達成里程碑: {message}")
        
        # 品質里程碑
        if creation_record["final_score"] >= 9.5:
            milestone = {
                "timestamp": datetime.now().isoformat(),
                "type": "quality_milestone",
                "message": f"🎯 創建了高品質角色 (分數: {creation_record['final_score']})",
                "role_id": creation_record["role_id"]
            }
            milestones.append(milestone)
        
        history["learning_milestones"] = milestones
    
    def _calculate_recent_average_score(self, history: Dict[str, Any]) -> float:
        """計算最近的平均分數"""
        trends = history.get("quality_trends", [])
        if not trends:
            return 0.0
        
        # 取最近 5 個記錄
        recent_scores = [trend["score"] for trend in trends[-5:]]
        return round(sum(recent_scores) / len(recent_scores), 2)
    
    def _update_pattern_evolution(self, history: Dict[str, Any], 
                                 user_input: Dict[str, str], score: float):
        """更新模式進化記錄"""
        if "pattern_evolution" not in history:
            history["pattern_evolution"] = {}
        
        evolution = history["pattern_evolution"]
        
        # 記錄語調進化
        tone = user_input.get('tone_style', '')
        if tone:
            if "tones" not in evolution:
                evolution["tones"] = {}
            if tone not in evolution["tones"]:
                evolution["tones"][tone] = {"count": 0, "avg_score": 0.0, "scores": []}
            
            evolution["tones"][tone]["count"] += 1
            evolution["tones"][tone]["scores"].append(score)
            evolution["tones"][tone]["avg_score"] = round(
                sum(evolution["tones"][tone]["scores"]) / len(evolution["tones"][tone]["scores"]), 2
            )
        
        # 記錄組織類型進化
        org_type = self._classify_organization(user_input.get('organization_name', ''))
        if "organization_types" not in evolution:
            evolution["organization_types"] = {}
        if org_type not in evolution["organization_types"]:
            evolution["organization_types"][org_type] = {"count": 0, "avg_score": 0.0, "scores": []}
        
        evolution["organization_types"][org_type]["count"] += 1
        evolution["organization_types"][org_type]["scores"].append(score)
        evolution["organization_types"][org_type]["avg_score"] = round(
            sum(evolution["organization_types"][org_type]["scores"]) / 
            len(evolution["organization_types"][org_type]["scores"]), 2
        )
    
    def _record_successful_pattern(self, history: Dict[str, Any], 
                                  user_input: Dict[str, str], score: float):
        """記錄成功模式"""
        if "successful_patterns" not in history:
            history["successful_patterns"] = []
        
        pattern = {
            "timestamp": datetime.now().isoformat(),
            "score": score,
            "role_name": user_input.get('role_name', ''),
            "organization_name": user_input.get('organization_name', ''),
            "tone_style": user_input.get('tone_style', ''),
            "has_special_requirements": bool(user_input.get('special_requirements', '')),
            "has_description": bool(user_input.get('description', ''))
        }
        
        history["successful_patterns"].append(pattern)
        
        # 只保留最近 20 個成功模式
        if len(history["successful_patterns"]) > 20:
            history["successful_patterns"] = history["successful_patterns"][-20:]
    
    def get_learning_insights(self) -> Dict[str, Any]:
        """獲取學習洞察"""
        history = self.load_history()
        
        insights = {
            "total_roles": history.get("total_roles_created", 0),
            "recent_average_score": self._calculate_recent_average_score(history),
            "milestones_count": len(history.get("learning_milestones", [])),
            "most_successful_tone": None,
            "most_common_org_type": None,
            "improvement_trend": None
        }
        
        # 分析最成功的語調
        evolution = history.get("pattern_evolution", {})
        tones = evolution.get("tones", {})
        if tones:
            best_tone = max(tones.items(), key=lambda x: x[1]["avg_score"])
            insights["most_successful_tone"] = {
                "tone": best_tone[0],
                "avg_score": best_tone[1]["avg_score"],
                "count": best_tone[1]["count"]
            }
        
        # 分析最常見的組織類型
        org_types = evolution.get("organization_types", {})
        if org_types:
            most_common = max(org_types.items(), key=lambda x: x[1]["count"])
            insights["most_common_org_type"] = {
                "type": most_common[0],
                "count": most_common[1]["count"],
                "avg_score": most_common[1]["avg_score"]
            }
        
        # 分析改進趨勢
        trends = history.get("quality_trends", [])
        if len(trends) >= 3:
            recent_scores = [t["score"] for t in trends[-3:]]
            early_scores = [t["score"] for t in trends[:3]]
            
            recent_avg = sum(recent_scores) / len(recent_scores)
            early_avg = sum(early_scores) / len(early_scores)
            
            if recent_avg > early_avg + 0.5:
                insights["improvement_trend"] = "顯著改善"
            elif recent_avg > early_avg:
                insights["improvement_trend"] = "穩定改善"
            else:
                insights["improvement_trend"] = "保持穩定"
        
        return insights
