#!/usr/bin/env python3
"""
重新訓練 Rasa 模型
"""

import subprocess
import os
import sys

def retrain_rasa():
    """重新訓練 Rasa 模型"""
    print("🚀 開始重新訓練 Rasa 模型")
    print("=" * 50)
    
    # 切換到 rasa 目錄
    rasa_dir = "rasa"
    if not os.path.exists(rasa_dir):
        print("❌ 找不到 rasa 目錄")
        return False
    
    original_dir = os.getcwd()
    
    try:
        os.chdir(rasa_dir)
        print(f"📁 切換到目錄: {os.getcwd()}")
        
        # 檢查 rasa 是否安裝
        print("🔍 檢查 Rasa 安裝...")
        result = subprocess.run(["rasa", "--version"], 
                              capture_output=True, text=True)
        
        if result.returncode != 0:
            print("❌ Rasa 未安裝或無法執行")
            print("💡 請先安裝 Rasa: pip install rasa")
            return False
        
        print(f"✅ Rasa 版本: {result.stdout.strip()}")
        
        # 訓練模型
        print("\n🧠 開始訓練模型...")
        print("⏳ 這可能需要幾分鐘時間...")
        
        train_result = subprocess.run([
            "rasa", "train", 
            "--config", "config.yml",
            "--domain", "domain.yml", 
            "--data", "data"
        ], capture_output=True, text=True)
        
        if train_result.returncode == 0:
            print("✅ 模型訓練成功！")
            print("📦 新模型已保存到 models/ 目錄")
            
            # 列出模型文件
            models_dir = "models"
            if os.path.exists(models_dir):
                models = [f for f in os.listdir(models_dir) if f.endswith('.tar.gz')]
                if models:
                    latest_model = max(models, key=lambda x: os.path.getctime(os.path.join(models_dir, x)))
                    print(f"🎯 最新模型: {latest_model}")
            
            return True
        else:
            print("❌ 模型訓練失敗")
            print("錯誤輸出:")
            print(train_result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 訓練過程出錯: {e}")
        return False
    
    finally:
        os.chdir(original_dir)

def validate_config():
    """驗證配置文件"""
    print("\n🔍 驗證配置文件")
    print("=" * 50)
    
    rasa_dir = "rasa"
    original_dir = os.getcwd()
    
    try:
        os.chdir(rasa_dir)
        
        # 驗證配置
        validate_result = subprocess.run([
            "rasa", "data", "validate"
        ], capture_output=True, text=True)
        
        if validate_result.returncode == 0:
            print("✅ 配置文件驗證通過")
            return True
        else:
            print("❌ 配置文件有問題")
            print("錯誤輸出:")
            print(validate_result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 驗證過程出錯: {e}")
        return False
    
    finally:
        os.chdir(original_dir)

def main():
    """主函數"""
    print("🎯 Rasa 模型重新訓練")
    print("=" * 60)
    
    # 1. 驗證配置
    if not validate_config():
        print("⚠️ 配置驗證失敗，但繼續嘗試訓練...")
    
    # 2. 重新訓練
    success = retrain_rasa()
    
    if success:
        print("\n🎉 訓練完成！")
        print("\n📋 接下來的步驟:")
        print("1. 重新啟動 Rasa 服務")
        print("2. 測試「高科的地址」是否會詢問校區")
        print("3. 測試多輪對話是否正常工作")
    else:
        print("\n❌ 訓練失敗")
        print("\n💡 可能的解決方案:")
        print("1. 檢查 Rasa 是否正確安裝")
        print("2. 檢查配置文件語法")
        print("3. 查看詳細錯誤訊息")

if __name__ == "__main__":
    main()
