recipe: default.v1
assistant_id: 20250703-051614-chalky-span
language: zh  # ⚠️ 中文請設為 zh

pipeline:
  - name: JiebaTokenizer        # 中文斷詞
  - name: RegexFeaturizer       # 支援意圖/實體使用 regex
  - name: LexicalSyntacticFeaturizer
  - name: CountVectorsFeaturizer
  - name: CountVectorsFeaturizer
    analyzer: char_wb
    min_ngram: 1
    max_ngram: 4
  - name: DIETClassifier         # 意圖分類 + 實體辨識
    epochs: 100
    constrain_similarities: true
  - name: EntitySynonymMapper    # 同義詞
  - name: ResponseSelector       # 回覆預設回應
    epochs: 100
  - name: FallbackClassifier     # 模糊意圖 fallback
    threshold: 0.3
    ambiguity_threshold: 0.1

policies:
  - name: MemoizationPolicy      # 記住常見對話
  - name: RulePolicy             # 支援 rule.yml 的規則
    core_fallback_threshold: 0.3
    core_fallback_action_name: "action_fallback_handle"
    enable_fallback_prediction: true

  - name: UnexpecTEDIntentPolicy # 防止亂跳流程
    max_history: 5
    epochs: 100
  - name: TEDPolicy              # ML對話政策
    max_history: 5
    epochs: 100
    constrain_similarities: true


# # The config recipe.
# # https://rasa.com/docs/rasa/model-configuration/
# recipe: default.v1

# # The assistant project unique identifier
# # This default value must be replaced with a unique assistant name within your deployment
# assistant_id: 20250703-051614-chalky-span

# # Configuration for Rasa NLU.
# # https://rasa.com/docs/rasa/nlu/components/
# language: en

# pipeline: null
# # # No configuration for the NLU pipeline was provided. The following default pipeline was used to train your model.
# # # If you'd like to customize it, uncomment and adjust the pipeline.
# # # See https://rasa.com/docs/rasa/tuning-your-model for more information.
# #   - name: WhitespaceTokenizer
# #   - name: RegexFeaturizer
# #   - name: LexicalSyntacticFeaturizer
# #   - name: CountVectorsFeaturizer
# #   - name: CountVectorsFeaturizer
# #     analyzer: char_wb
# #     min_ngram: 1
# #     max_ngram: 4
# #   - name: DIETClassifier
# #     epochs: 100
# #     constrain_similarities: true
# #   - name: EntitySynonymMapper
# #   - name: ResponseSelector
# #     epochs: 100
# #     constrain_similarities: true
# #   - name: FallbackClassifier
# #     threshold: 0.3
# #     ambiguity_threshold: 0.1

# # Configuration for Rasa Core.
# # https://rasa.com/docs/rasa/core/policies/
# policies: null
# # # No configuration for policies was provided. The following default policies were used to train your model.
# # # If you'd like to customize them, uncomment and adjust the policies.
# # # See https://rasa.com/docs/rasa/policies for more information.
# #   - name: MemoizationPolicy
# #   - name: RulePolicy
# #   - name: UnexpecTEDIntentPolicy
# #     max_history: 5
# #     epochs: 100
# #   - name: TEDPolicy
# #     max_history: 5
# #     epochs: 100
# #     constrain_similarities: true
