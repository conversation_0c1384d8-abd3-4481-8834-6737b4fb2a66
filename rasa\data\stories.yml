version: "3.1"

stories:

- story: happy path
  steps:
  - intent: greet
  - action: utter_greet
  - intent: mood_great
  - action: utter_happy

- story: sad path 1
  steps:
  - intent: greet
  - action: utter_greet
  - intent: mood_unhappy
  - action: utter_cheer_up
  - action: utter_did_that_help
  - intent: affirm
  - action: utter_happy

- story: sad path 2
  steps:
  - intent: greet
  - action: utter_greet
  - intent: mood_unhappy
  - action: utter_cheer_up
  - action: utter_did_that_help
  - intent: deny
  - action: utter_goodbye

- story: 查詢地址 - 需要詢問校區
  steps:
  - intent: ask_office_location
  - action: utter_ask_campus
  - intent: inform
    entities:
    - campus: "楠梓校區"
  - slot_was_set:
    - campus: "楠梓校區"
  - action: utter_reply_campus

- story: 查詢地址 - 需要詢問校區 2
  steps:
  - intent: ask_office_location
  - action: utter_ask_campus
  - intent: inform
    entities:
    - campus: "建工校區"
  - slot_was_set:
    - campus: "建工校區"
  - action: utter_reply_campus

- story: 查詢地址 - 需要詢問校區 3
  steps:
  - intent: ask_office_location
  - action: utter_ask_campus
  - intent: inform
    entities:
    - campus: "第一校區"
  - slot_was_set:
    - campus: "第一校區"
  - action: utter_reply_campus

- story: 查詢地址 - 直接提供校區
  steps:
  - intent: ask_office_location
    entities:
    - campus: "楠梓校區"
  - slot_was_set:
    - campus: "楠梓校區"
  - action: utter_reply_campus

- story: 查詢地址 - 直接提供建工校區
  steps:
  - intent: ask_office_location
    entities:
    - campus: "建工校區"
  - slot_was_set:
    - campus: "建工校區"
  - action: utter_reply_campus
