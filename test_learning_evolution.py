#!/usr/bin/env python3
"""
測試學習和進化機制
"""

import asyncio
import json
from plugins.role_creator import RoleCreator
from plugins.learning_history import LearningHistory

async def test_multiple_role_creation():
    """測試創建多個角色來驗證學習機制"""
    print("🧪 測試多角色創建和學習機制")
    print("=" * 60)
    
    role_creator = RoleCreator()
    
    # 創建多個不同類型的角色來測試學習
    test_roles = [
        {
            "role_name": "客服專員",
            "organization_name": "星光科技公司",
            "organization_domain": "startech.com",
            "tone_style": "友善專業",
            "special_requirements": "處理技術支援和產品諮詢",
            "description": "專業的客戶服務代表"
        },
        {
            "role_name": "學術顧問",
            "organization_name": "創新大學",
            "organization_domain": "innovation-uni.edu.tw",
            "tone_style": "專業親切",
            "special_requirements": "協助學生選課和學術規劃",
            "description": "提供學術指導和建議"
        },
        {
            "role_name": "醫療助理",
            "organization_name": "健康醫院",
            "organization_domain": "health-hospital.com.tw",
            "tone_style": "溫和關懷",
            "special_requirements": "提供醫療資訊和預約協助",
            "description": "協助患者獲得醫療服務"
        },
        {
            "role_name": "財務顧問",
            "organization_name": "智慧金融",
            "organization_domain": "smartfin.com",
            "tone_style": "專業可靠",
            "special_requirements": "提供投資建議和財務規劃",
            "description": "專業的財務諮詢服務"
        }
    ]
    
    created_roles = []
    
    for i, role_data in enumerate(test_roles, 1):
        print(f"\n🔄 創建第 {i} 個角色: {role_data['role_name']}")
        print(f"   組織: {role_data['organization_name']}")
        print(f"   語調: {role_data['tone_style']}")
        
        result = await role_creator.create_custom_role(role_data)
        
        if result['success']:
            print(f"   ✅ 成功! 分數: {result['final_score']}/10, 迭代: {result['iterations']}")
            created_roles.append({
                "role_id": result['role_id'],
                "score": result['final_score'],
                "iterations": result['iterations'],
                "role_data": role_data
            })
        else:
            print(f"   ❌ 失敗: {result['error']}")
    
    return created_roles

def test_learning_insights():
    """測試學習洞察功能"""
    print("\n🧪 測試學習洞察")
    print("=" * 60)
    
    learning_history = LearningHistory()
    insights = learning_history.get_learning_insights()
    
    print("📊 學習洞察結果:")
    print(f"  總角色數: {insights['total_roles']}")
    print(f"  最近平均分數: {insights['recent_average_score']}")
    print(f"  里程碑數量: {insights['milestones_count']}")
    
    if insights['most_successful_tone']:
        tone_info = insights['most_successful_tone']
        print(f"  最成功語調: {tone_info['tone']} (平均 {tone_info['avg_score']} 分, {tone_info['count']} 次)")
    
    if insights['most_common_org_type']:
        org_info = insights['most_common_org_type']
        print(f"  最常見組織: {org_info['type']} ({org_info['count']} 個, 平均 {org_info['avg_score']} 分)")
    
    if insights['improvement_trend']:
        print(f"  改進趨勢: {insights['improvement_trend']}")
    
    return insights

def test_pattern_analysis():
    """測試模式分析功能"""
    print("\n🧪 測試模式分析")
    print("=" * 60)
    
    from plugins.prompt_generator import PromptGenerator
    
    generator = PromptGenerator()
    configs = generator.load_existing_configs()
    patterns = generator.analyze_successful_patterns(configs)
    
    print("🔍 分析的成功模式:")
    print(patterns)
    
    return patterns

def test_evolution_summary():
    """測試進化總結功能"""
    print("\n🧪 測試進化總結")
    print("=" * 60)
    
    role_creator = RoleCreator()
    evolution_summary = role_creator.get_evolution_summary()
    
    print("📈 系統進化總結:")
    print(evolution_summary)
    
    return evolution_summary

def view_learning_history():
    """查看完整的學習歷史"""
    print("\n🧪 查看學習歷史")
    print("=" * 60)
    
    learning_history = LearningHistory()
    history = learning_history.load_history()
    
    print("📚 學習歷史概覽:")
    print(f"  創建日期: {history.get('creation_date', '未知')}")
    print(f"  總角色數: {history.get('total_roles_created', 0)}")
    
    # 顯示里程碑
    milestones = history.get('learning_milestones', [])
    if milestones:
        print(f"\n🏆 里程碑 ({len(milestones)} 個):")
        for milestone in milestones[-5:]:  # 顯示最近 5 個
            print(f"  - {milestone.get('message', '未知里程碑')}")
    
    # 顯示品質趨勢
    trends = history.get('quality_trends', [])
    if trends:
        print(f"\n📈 品質趨勢 (最近 5 個):")
        for trend in trends[-5:]:
            print(f"  - 分數: {trend['score']}, 迭代: {trend['iterations']}, 總數: {trend['role_count']}")
    
    # 顯示成功模式
    successful_patterns = history.get('successful_patterns', [])
    if successful_patterns:
        print(f"\n🌟 成功模式 (最近 3 個):")
        for pattern in successful_patterns[-3:]:
            print(f"  - {pattern['role_name']} @ {pattern['organization_name']} (分數: {pattern['score']})")
    
    return history

async def demonstrate_self_improvement():
    """演示自我改進過程"""
    print("\n🧪 演示自我改進過程")
    print("=" * 60)
    
    # 創建一個相似的角色，看看系統是否學會了
    role_creator = RoleCreator()
    
    # 第一次創建
    first_role = {
        "role_name": "技術支援",
        "organization_name": "測試科技",
        "organization_domain": "test-tech.com",
        "tone_style": "專業友善",
        "special_requirements": "解決技術問題",
        "description": "提供技術支援服務"
    }
    
    print("🔄 創建第一個測試角色...")
    result1 = await role_creator.create_custom_role(first_role)
    print(f"結果: 分數 {result1['final_score']}, 迭代 {result1['iterations']}")
    
    # 稍等一下，然後創建類似的角色
    print("\n🔄 創建類似的第二個角色...")
    second_role = {
        "role_name": "技術顧問",
        "organization_name": "另一家科技公司",
        "organization_domain": "another-tech.com",
        "tone_style": "專業友善",  # 相同的語調
        "special_requirements": "提供技術諮詢",
        "description": "專業技術諮詢服務"
    }
    
    result2 = await role_creator.create_custom_role(second_role)
    print(f"結果: 分數 {result2['final_score']}, 迭代 {result2['iterations']}")
    
    # 分析改進
    if result2['final_score'] >= result1['final_score'] and result2['iterations'] <= result1['iterations']:
        print("🎉 系統顯示出學習改進！第二個角色品質更好且迭代更少")
    elif result2['final_score'] > result1['final_score']:
        print("📈 系統學習中，第二個角色品質有所提升")
    else:
        print("📊 系統保持穩定的品質水準")
    
    return result1, result2

async def main():
    """主測試函數"""
    print("🚀 開始測試學習和進化機制")
    print("=" * 80)
    
    try:
        # 1. 查看當前學習歷史
        view_learning_history()
        
        # 2. 測試學習洞察
        test_learning_insights()
        
        # 3. 測試模式分析
        test_pattern_analysis()
        
        # 4. 測試進化總結
        test_evolution_summary()
        
        # 5. 創建多個角色測試學習
        created_roles = await test_multiple_role_creation()
        
        # 6. 再次查看學習洞察，看看變化
        print("\n" + "=" * 60)
        print("📊 創建角色後的學習洞察變化:")
        test_learning_insights()
        
        # 7. 演示自我改進
        await demonstrate_self_improvement()
        
        print("\n🎉 學習和進化機制測試完成！")
        print("=" * 80)
        
        # 總結
        print("\n📋 測試總結:")
        print(f"  - 本次創建角色: {len(created_roles)}")
        print(f"  - 平均分數: {sum(r['score'] for r in created_roles) / len(created_roles):.1f}" if created_roles else "  - 無角色創建")
        print(f"  - 學習機制: ✅ 正常運作")
        print(f"  - 進化機制: ✅ 正常運作")
        print(f"  - 歷史記錄: ✅ 正常儲存")
        
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
