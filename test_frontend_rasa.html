<!DOCTYPE html>
<html>
<head>
    <title>測試 Rasa 前端邏輯</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>🧪 Rasa 前端邏輯測試</h1>
    
    <div class="test-section">
        <h2>1. 測試 Rasa 服務連接</h2>
        <button onclick="testRasaConnection()">測試 Rasa 連接</button>
        <div id="rasa-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 測試信心度判斷邏輯</h2>
        <button onclick="testConfidenceLogic()">測試信心度邏輯</button>
        <div id="confidence-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 測試完整流程</h2>
        <input type="text" id="test-message" placeholder="輸入測試訊息" value="今天有什麼活動嗎？" style="width: 300px; padding: 5px;">
        <button onclick="testFullFlow()">測試完整流程</button>
        <div id="flow-result" class="result"></div>
    </div>

    <script>
        const user_id = "test_user_" + Date.now();
        
        // 複製前端的 tryRasaResponse 函數
        async function tryRasaResponse(message) {
            try {
                console.log("🤖 嘗試 Rasa 回應:", message);
                
                const response = await fetch("https://b225.54ucl.com/ui/rasa", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({
                        message: message,
                        user_id: user_id,
                        model_id: "rasa"
                    })
                });

                if (!response.ok) {
                    throw new Error(`Rasa 服務錯誤 (${response.status})`);
                }

                const data = await response.json();
                console.log("🤖 Rasa 回應數據:", data);

                // 解析 Rasa 回應
                if (Array.isArray(data) && data.length > 0) {
                    const text = data.map(item => item.text).join("\n");
                    
                    // 根據回應內容判斷信心度
                    let confidence = 0.5;
                    let intent = 'unknown';
                    
                    if (text && text.length > 20) {
                        if (text.includes('嗨，我是高科大') || text.includes('智慧助理') || 
                            text.includes('你好') || text.includes('哈囉')) {
                            confidence = 0.9;
                            intent = 'greet';
                        } else if (text.includes('有什麼我可以幫你') || text.includes('需要什麼協助')) {
                            confidence = 0.85;
                            intent = 'help_offer';
                        } else {
                            confidence = 0.75;
                        }
                    }

                    if (text.includes('抱歉') || text.includes('不理解') || text.includes('不明白') ||
                        text.includes('請問您需要查詢') || text.includes('請提供更詳細') ||
                        text.includes('建議您') || text.includes('請針對您感興趣')) {
                        confidence = 0.3;
                        intent = 'fallback';
                    }

                    const shouldUseRasa = confidence >= 0.8;

                    return {
                        shouldUseRasa: shouldUseRasa,
                        confidence: confidence,
                        intent: intent,
                        response: text,
                        rawData: data
                    };
                } else {
                    return {
                        shouldUseRasa: false,
                        confidence: 0,
                        intent: 'unknown',
                        response: '抱歉，我無法理解您的問題。',
                        rawData: data
                    };
                }

            } catch (error) {
                console.error("🤖 Rasa 處理錯誤:", error);
                throw error;
            }
        }
        
        async function testRasaConnection() {
            const resultDiv = document.getElementById('rasa-result');
            resultDiv.innerHTML = '⏳ 測試中...';
            
            try {
                const response = await fetch("https://b225.54ucl.com/ui/rasa", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({
                        message: "你好",
                        user_id: user_id,
                        model_id: "rasa"
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        ✅ Rasa 連接成功<br>
                        狀態碼: ${response.status}<br>
                        回應: <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Rasa 連接失敗: ${response.status}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 連接錯誤: ${error.message}`;
            }
        }
        
        function testConfidenceLogic() {
            const resultDiv = document.getElementById('confidence-result');
            
            const testCases = [
                { text: "嗨，我是高科大的智慧助理", expected: "high" },
                { text: "你想查詢哪個校區呢？", expected: "low" },
                { text: "抱歉，我不理解您的問題", expected: "low" }
            ];
            
            let results = [];
            
            testCases.forEach(testCase => {
                let confidence = 0.5;
                const text = testCase.text;
                
                if (text && text.length > 20) {
                    if (text.includes('嗨，我是高科大') || text.includes('智慧助理')) {
                        confidence = 0.9;
                    } else if (text.includes('有什麼我可以幫你')) {
                        confidence = 0.85;
                    } else {
                        confidence = 0.75;
                    }
                }
                
                if (text.includes('抱歉') || text.includes('不理解')) {
                    confidence = 0.3;
                }
                
                const shouldUseRasa = confidence >= 0.8;
                const actual = shouldUseRasa ? "high" : "low";
                const passed = actual === testCase.expected;
                
                results.push({
                    text: testCase.text,
                    confidence: confidence,
                    expected: testCase.expected,
                    actual: actual,
                    passed: passed
                });
            });
            
            resultDiv.innerHTML = results.map(r => `
                <div style="margin: 5px 0; padding: 5px; background: ${r.passed ? '#d4edda' : '#f8d7da'}">
                    ${r.passed ? '✅' : '❌'} "${r.text}"<br>
                    信心度: ${r.confidence}, 預期: ${r.expected}, 實際: ${r.actual}
                </div>
            `).join('');
        }
        
        async function testFullFlow() {
            const resultDiv = document.getElementById('flow-result');
            const message = document.getElementById('test-message').value;
            
            resultDiv.innerHTML = '⏳ 測試完整流程...';
            
            try {
                console.log("🧪 開始測試完整流程");
                
                // 第一階段：Rasa 處理
                const rasaResult = await tryRasaResponse(message);
                
                let flowResult = `
                    <h4>🤖 Rasa 階段結果：</h4>
                    <ul>
                        <li>信心度: ${rasaResult.confidence.toFixed(2)}</li>
                        <li>意圖: ${rasaResult.intent}</li>
                        <li>使用 Rasa: ${rasaResult.shouldUseRasa ? '✅ 是' : '❌ 否'}</li>
                        <li>回應: ${rasaResult.response}</li>
                    </ul>
                `;
                
                if (rasaResult.shouldUseRasa) {
                    flowResult += `<h4>✅ 流程結束：由 Rasa 處理</h4>`;
                    resultDiv.className = 'result success';
                } else {
                    flowResult += `<h4>🔄 轉交 AI Agent 處理</h4>`;
                    flowResult += `<p>⚠️ 注意：這裡應該會調用後端 AI，但在測試中跳過</p>`;
                    resultDiv.className = 'result';
                }
                
                resultDiv.innerHTML = flowResult;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 測試失敗: ${error.message}`;
            }
        }
    </script>
</body>
</html>
