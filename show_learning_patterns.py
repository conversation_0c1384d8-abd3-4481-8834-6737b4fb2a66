#!/usr/bin/env python3
"""
展示系統學習到的模式
"""

from plugins.prompt_generator import PromptGenerator
from plugins.learning_history import LearningHistory

def show_successful_patterns():
    """展示系統分析的成功模式"""
    print("🧠 系統學習到的成功模式")
    print("=" * 60)
    
    generator = PromptGenerator()
    
    # 載入現有配置
    configs = generator.load_existing_configs()
    print(f"📚 載入了 {len(configs)} 個角色配置")
    
    # 分析成功模式
    patterns = generator.analyze_successful_patterns(configs)
    
    print("\n🔍 分析結果:")
    print("-" * 40)
    print(patterns)
    
    return patterns

def show_evolution_summary():
    """展示進化總結"""
    print("\n📈 系統進化總結")
    print("=" * 60)
    
    generator = PromptGenerator()
    evolution_summary = generator._get_evolution_summary()
    
    print("🎯 進化洞察:")
    print("-" * 40)
    print(evolution_summary)
    
    return evolution_summary

def show_learning_insights():
    """展示學習洞察"""
    print("\n💡 詳細學習洞察")
    print("=" * 60)
    
    learning_history = LearningHistory()
    insights = learning_history.get_learning_insights()
    
    print("📊 統計數據:")
    print(f"  總角色數: {insights['total_roles']}")
    print(f"  最近平均分數: {insights['recent_average_score']}")
    print(f"  里程碑數量: {insights['milestones_count']}")
    
    if insights['most_successful_tone']:
        tone_info = insights['most_successful_tone']
        print(f"  最成功語調: {tone_info['tone']} (平均 {tone_info['avg_score']} 分, {tone_info['count']} 次)")
    
    if insights['most_common_org_type']:
        org_info = insights['most_common_org_type']
        print(f"  最常見組織: {org_info['type']} ({org_info['count']} 個, 平均 {org_info['avg_score']} 分)")
    
    if insights['improvement_trend']:
        print(f"  改進趨勢: {insights['improvement_trend']}")
    
    return insights

def show_actual_prompts_analysis():
    """分析實際的 System Prompt 內容"""
    print("\n📝 實際 System Prompt 分析")
    print("=" * 60)
    
    generator = PromptGenerator()
    configs = generator.load_existing_configs()
    
    # 找出所有有 system_prompt 的角色
    prompts_with_content = {}
    for role_id, config in configs.items():
        system_prompt = config.get('system_prompt', '')
        if system_prompt and len(system_prompt) > 100:
            prompts_with_content[role_id] = {
                'prompt': system_prompt,
                'length': len(system_prompt),
                'role_name': config.get('role_title', '未知'),
                'org_name': config.get('organization_name', '未知'),
                'tone': config.get('tone', '未知')
            }
    
    print(f"🎭 找到 {len(prompts_with_content)} 個有完整 System Prompt 的角色:")
    
    for role_id, info in prompts_with_content.items():
        print(f"\n  📋 {role_id}:")
        print(f"     角色: {info['role_name']} @ {info['org_name']}")
        print(f"     語調: {info['tone']}")
        print(f"     長度: {info['length']} 字元")
        
        # 分析開頭
        first_line = info['prompt'].split('\n')[0]
        print(f"     開頭: {first_line[:50]}...")
        
        # 檢查是否包含成功模式
        success_patterns = ['1. **首先**', '2. **接著**', '智能搜尋流程', '網址選擇原則']
        found_patterns = [pattern for pattern in success_patterns if pattern in info['prompt']]
        if found_patterns:
            print(f"     包含模式: {', '.join(found_patterns)}")
    
    return prompts_with_content

def demonstrate_reference_usage():
    """演示參考機制如何運作"""
    print("\n🔄 參考機制演示")
    print("=" * 60)
    
    generator = PromptGenerator()
    
    # 模擬一個新的用戶輸入
    test_input = {
        "role_name": "測試顧問",
        "organization_name": "測試公司",
        "organization_domain": "test.com",
        "tone_style": "專業親切",  # 使用已知成功的語調
        "special_requirements": "提供專業建議",
        "description": "這是一個測試角色"
    }
    
    print("🎯 模擬創建新角色時的參考過程:")
    print(f"  用戶輸入: {test_input['role_name']} @ {test_input['organization_name']}")
    print(f"  選擇語調: {test_input['tone_style']}")
    
    # 載入現有配置並分析
    configs = generator.load_existing_configs()
    patterns = generator.analyze_successful_patterns(configs)
    evolution_summary = generator._get_evolution_summary()
    
    print(f"\n📚 系統會參考:")
    print(f"  - 現有 {len(configs)} 個角色的成功經驗")
    print(f"  - 分析出的成功模式")
    print(f"  - 進化洞察和趨勢")
    
    print(f"\n🧠 參考內容預覽:")
    print("  成功模式:")
    for line in patterns.split('\n')[:3]:
        if line.strip():
            print(f"    • {line}")
    
    print("  進化洞察:")
    for line in evolution_summary.split('\n')[:3]:
        if line.strip():
            print(f"    • {line}")
    
    print(f"\n✨ 基於這些參考，系統會生成更優質的 System Prompt")

def main():
    """主函數"""
    print("🚀 展示系統學習和參考機制")
    print("=" * 80)
    
    # 1. 展示成功模式分析
    show_successful_patterns()
    
    # 2. 展示進化總結
    show_evolution_summary()
    
    # 3. 展示學習洞察
    show_learning_insights()
    
    # 4. 分析實際 prompt 內容
    show_actual_prompts_analysis()
    
    # 5. 演示參考機制
    demonstrate_reference_usage()
    
    print("\n🎉 展示完成！")
    print("💡 系統確實會參考成功的 prompt 來生成更好的新 prompt")

if __name__ == "__main__":
    main()
