#!/usr/bin/env python3
"""
調試 PDF 處理功能
"""

import asyncio
import sys
import os

# 添加當前目錄到路徑
sys.path.append('.')

async def test_pdf_extraction():
    """測試 PDF 文字提取功能"""
    print("🔍 測試 PDF 文字提取功能")
    print("=" * 50)
    
    try:
        from web_app import extract_pdf_text, PDF_AVAILABLE
        
        print(f"📋 PDF_AVAILABLE: {PDF_AVAILABLE}")
        
        if not PDF_AVAILABLE:
            print("❌ PDF 處理套件不可用")
            print("請安裝：pip install pdfplumber 或 pip install PyPDF2")
            return False
        
        # 檢查是否有測試 PDF
        test_files = ["test.pdf", "sample.pdf", "example.pdf"]
        test_file = None
        
        for file in test_files:
            if os.path.exists(file):
                test_file = file
                break
        
        if not test_file:
            print("❌ 找不到測試 PDF 檔案")
            print("請準備一個 PDF 檔案並命名為 test.pdf")
            return False
        
        print(f"📄 使用測試檔案: {test_file}")
        
        # 讀取檔案
        with open(test_file, 'rb') as f:
            pdf_content = f.read()
        
        print(f"📊 檔案大小: {len(pdf_content)} bytes ({len(pdf_content)/1024:.1f} KB)")
        
        # 提取文字
        print("🔄 開始提取文字...")
        extracted_text = await extract_pdf_text(pdf_content, test_file)
        
        print(f"📝 提取結果:")
        print(f"   長度: {len(extracted_text)} 字符")
        print(f"   類型: {type(extracted_text)}")
        
        if extracted_text.startswith("❌") or extracted_text.startswith("⚠️"):
            print(f"❌ 提取失敗: {extracted_text}")
            return False
        
        print(f"📖 文字預覽 (前 300 字符):")
        print("-" * 40)
        print(extracted_text[:300])
        print("-" * 40)
        
        # 測試組合訊息
        user_message = "請分析這個 PDF 檔案"
        combined_message = f"""📄 **PDF 檔案分析**
檔案名稱: {test_file}
檔案大小: {len(pdf_content) / 1024:.1f} KB

**PDF 內容:**
{extracted_text}

**用戶問題:**
{user_message}

請根據上述 PDF 內容回答用戶的問題。如果用戶沒有具體問題，請提供 PDF 內容的摘要和重點分析。"""

        print(f"🔗 組合訊息:")
        print(f"   總長度: {len(combined_message)} 字符")
        print(f"   預覽 (前 500 字符):")
        print("-" * 40)
        print(combined_message[:500])
        print("-" * 40)
        
        return True
        
    except ImportError as e:
        print(f"❌ 導入錯誤: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_pdf_packages():
    """檢查 PDF 處理套件"""
    print("📦 檢查 PDF 處理套件")
    print("-" * 30)
    
    packages = [
        ("pdfplumber", "pdfplumber"),
        ("PyPDF2", "PyPDF2"),
        ("python-multipart", "multipart")
    ]
    
    available = []
    
    for pip_name, import_name in packages:
        try:
            __import__(import_name)
            print(f"✅ {pip_name}: 已安裝")
            available.append(pip_name)
        except ImportError:
            print(f"❌ {pip_name}: 未安裝")
    
    return available

def create_simple_test_pdf():
    """創建簡單的測試 PDF"""
    print("\n📝 嘗試創建測試 PDF")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        filename = "test.pdf"
        c = canvas.Canvas(filename, pagesize=letter)
        
        # 添加測試內容
        c.drawString(100, 750, "PDF 測試文件")
        c.drawString(100, 730, "這是一個用於測試的 PDF 檔案")
        c.drawString(100, 710, "")
        c.drawString(100, 690, "測試內容：")
        c.drawString(120, 670, "1. 中文文字測試")
        c.drawString(120, 650, "2. English text test")
        c.drawString(120, 630, "3. 數字測試: 123456789")
        c.drawString(120, 610, "4. 日期測試: 2025-01-08")
        
        # 第二頁
        c.showPage()
        c.drawString(100, 750, "第二頁內容")
        c.drawString(100, 730, "這是第二頁的測試內容")
        c.drawString(100, 710, "用於測試多頁 PDF 的處理")
        
        c.save()
        
        print(f"✅ 測試 PDF 已創建: {filename}")
        return True
        
    except ImportError:
        print("❌ reportlab 未安裝，無法創建測試 PDF")
        print("請手動準備一個 test.pdf 檔案")
        return False
    except Exception as e:
        print(f"❌ 創建失敗: {e}")
        return False

async def main():
    """主函數"""
    print("🚀 PDF 功能調試工具")
    print("=" * 60)
    
    # 1. 檢查套件
    available_packages = check_pdf_packages()
    
    if not any(pkg in available_packages for pkg in ["pdfplumber", "PyPDF2"]):
        print("\n❌ 沒有可用的 PDF 處理套件")
        print("請安裝：")
        print("   pip install pdfplumber")
        print("   或")
        print("   pip install PyPDF2")
        return
    
    # 2. 檢查測試檔案
    if not os.path.exists("test.pdf"):
        print("\n📄 未找到測試 PDF 檔案")
        if create_simple_test_pdf():
            print("✅ 已創建測試檔案")
        else:
            print("❌ 無法創建測試檔案，請手動準備 test.pdf")
            return
    
    # 3. 測試 PDF 提取
    print("\n" + "="*60)
    success = await test_pdf_extraction()
    
    if success:
        print("\n🎉 PDF 文字提取測試通過！")
        print("\n💡 如果模型仍然說沒看到 PDF 內容，可能的原因：")
        print("   1. 檢查瀏覽器開發者工具的網路請求")
        print("   2. 檢查後端日誌輸出")
        print("   3. 確認 PDF 檔案確實被上傳")
        print("   4. 檢查模型是否正確接收到組合訊息")
        
        print("\n🔧 調試建議：")
        print("   1. 重啟 web 應用程序")
        print("   2. 檢查瀏覽器控制台是否有錯誤")
        print("   3. 查看後端日誌中的 PDF 處理訊息")
        
    else:
        print("\n❌ PDF 文字提取測試失敗")
        print("請檢查錯誤訊息並修復問題")

if __name__ == "__main__":
    asyncio.run(main())
