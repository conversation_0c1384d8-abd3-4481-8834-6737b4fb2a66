#!/usr/bin/env python3
"""
簡化版 Web 應用，用於測試 Rasa 整合
不依賴 semantic_kernel，模擬 AI 回應
"""

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import asyncio
import json
import time

app = FastAPI()

# CORS 設定
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 請求模型
class ChatRequest(BaseModel):
    user_id: str
    message: str
    model_id: str
    mode: str = "default"
    rasa_context: dict = None

# 模擬 AI 回應
async def simulate_ai_response(message: str, rasa_context: dict = None):
    """模擬 AI 回應的串流"""
    
    # 模擬搜尋結果
    search_result = {
        "searchResults": [
            {
                "url": "https://nkust.edu.tw/events",
                "title": "高科大活動資訊",
                "summary": "高雄科技大學最新活動公告"
            }
        ]
    }
    
    # 先返回搜尋結果
    yield json.dumps(search_result, ensure_ascii=False) + "\n---\n"
    
    # 模擬思考時間
    await asyncio.sleep(0.5)
    
    # 根據 Rasa 上下文生成回應
    if rasa_context:
        context_info = f"根據 Rasa 分析，您的問題意圖是：{rasa_context.get('intent', 'unknown')}，信心度：{rasa_context.get('confidence', 0):.2f}\n\n"
    else:
        context_info = ""
    
    # 模擬 AI 回應內容
    if "活動" in message:
        response_parts = [
            f"{context_info}經過深度分析，為您查詢今日活動資訊：\n\n",
            "📅 **今日活動安排**\n",
            "1. 學術研討會 - 工程館3樓\n",
            "   時間：14:00-16:00\n",
            "   主題：AI 技術應用\n\n",
            "2. 社團博覽會 - 中庭廣場\n",
            "   時間：10:00-17:00\n",
            "   內容：各社團招生展示\n\n",
            "📍 **參考來源：**\n",
            "- https://nkust.edu.tw/events\n",
            "- https://nkust.edu.tw/news"
        ]
    elif "課程" in message:
        response_parts = [
            f"{context_info}為您查詢高科大課程資訊：\n\n",
            "🎓 **主要學院課程**\n",
            "• 工程學院：電機、機械、資訊工程\n",
            "• 管理學院：企管、國貿、財金\n",
            "• 設計學院：工設、建築、數媒\n\n",
            "📚 詳細課程請參考：\n",
            "- https://nkust.edu.tw/academics"
        ]
    else:
        response_parts = [
            f"{context_info}感謝您的提問！\n\n",
            "我是高科大的智能助理，可以協助您：\n",
            "• 查詢校園活動資訊\n",
            "• 了解課程和學系\n",
            "• 校園生活指導\n\n",
            "請告訴我您想了解什麼？"
        ]
    
    # 逐步返回回應
    for part in response_parts:
        yield part
        await asyncio.sleep(0.3)  # 模擬打字效果

@app.post("/ui/chat")
async def chat(req: ChatRequest):
    """聊天端點"""
    print(f"📥 收到請求: {req.message}")
    if req.rasa_context:
        print(f"🤖 Rasa 上下文: {req.rasa_context}")
    
    async def stream_response():
        async for chunk in simulate_ai_response(req.message, req.rasa_context):
            yield chunk
    
    return StreamingResponse(stream_response(), media_type="text/plain")

@app.get("/ui/roles")
async def get_roles():
    """獲取角色列表"""
    return {
        "default": {
            "display_name": "預設助理",
            "organization_name": "高科大",
            "role_title": "智能助理"
        },
        "nkust_assistant": {
            "display_name": "高科大助理",
            "organization_name": "高雄科技大學",
            "role_title": "校園助理"
        }
    }

@app.get("/health")
async def health_check():
    """健康檢查"""
    return {"status": "ok", "message": "簡化版服務運行正常"}

# 靜態文件
app.mount("/ui", StaticFiles(directory="static", html=True), name="static")

if __name__ == "__main__":
    import uvicorn
    print("🚀 啟動簡化版 Web 服務...")
    print("📍 服務地址: http://localhost:8088")
    print("🌐 前端界面: http://localhost:8088/ui/")
    print("💡 這是測試版本，模擬 AI 回應")
    uvicorn.run(app, host="0.0.0.0", port=8088)
