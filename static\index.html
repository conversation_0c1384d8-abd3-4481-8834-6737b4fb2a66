<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>🤖 高科大 AI 助理</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    .fade-in{animation:fadeIn .5s ease-in-out}
    @keyframes fadeIn{
      from{opacity:0;transform:translateY(10px)}
      to{opacity:1;transform:translateY(0)}
    }

    /* 自定義滾動條 */
    .custom-scrollbar::-webkit-scrollbar {
      width: 8px;
    }
    .custom-scrollbar::-webkit-scrollbar-track {
      background: #374151;
      border-radius: 4px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
      background: #6366f1;
      border-radius: 4px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background: #4f46e5;
    }

    /* 響應式設計 */
    @media (max-width: 768px) {
      .desktop-layout {
        flex-direction: column;
      }
      .sidebar {
        width: 100% !important;
        position: relative !important;
        height: auto !important;
      }
      .main-content {
        height: calc(100vh - 120px) !important;
      }
    }
  </style>
</head>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen text-gray-100 overflow-hidden">

  <div class="desktop-layout flex h-screen">
    <!-- 左側：固定側邊欄 -->
    <aside class="sidebar w-80 bg-gray-800/90 backdrop-blur-sm border-r border-gray-700 p-6 flex flex-col fixed h-full z-10">
      <!-- 標題區 -->
      <div class="mb-8">
        <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-2">
          🤖 高科大 AI 助理
        </h1>
        <p class="text-gray-400 text-sm">智慧問答 • 即時搜尋 • 專業回覆</p>
      </div>

      <!-- 模型選擇 -->
      <div class="mb-6">
        <h2 class="text-lg font-semibold mb-4 flex items-center">
          <span class="mr-2">🧠</span>
          選擇 AI 模型
        </h2>
        <select id="model" class="w-full p-3 rounded-lg bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all">
          <option value="o3-mini">🎯 o3-mini (平衡性能)</option>
          <option value="deepseek">🔍 DeepSeek R1 (聯網搜尋)</option>
          <option value="gpt-4.1-nano">⚡ GPT-4.1 Nano (快速回應)</option>
        </select>
        <div class="text-xs text-gray-500 mt-2">
          💡 DeepSeek 支援即時搜尋功能
        </div>
      </div>

      <!-- 模式選擇 -->
    <div class="mt-4">
      <h2 class="text-sm font-semibold mb-2 flex items-center text-gray-300">
        <span class="mr-2">🎭</span> 選擇角色模式
      </h2>
      <select id="mode" class="w-full p-3 rounded-lg bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all">
        <option value="nkust_assistant">🏫 高科大助理（NKUST）</option>
        <option value="default">填表單助手（default）</option>
        <option value="cfg:corporate">企業助理</option>
        <option value="cfg:teacher">👨‍🏫 教授助理</option>
        <option value="cfg:creative">🎨 創作助理</option>
        <!-- 動態載入的自定義角色會插入到這裡 -->
        <option value="custom_create" class="bg-purple-600">✨ 自訂義角色</option>
      </select>

      <!-- 角色管理按鈕 -->
      <div class="mt-2 flex gap-2">
        <button id="refresh-roles" class="text-xs text-gray-400 hover:text-white transition-colors">
          🔄 重新載入角色
        </button>
        <button id="manage-roles" class="text-xs text-gray-400 hover:text-white transition-colors">
          ⚙️ 管理角色
        </button>
        <button id="view-system-prompt" class="text-xs text-gray-400 hover:text-white transition-colors">
          📄 查看 System Prompt
        </button>
      </div>
    </div>


      <!-- 功能說明 -->
      <div class="mt-auto">
        <div class="bg-gray-700/50 rounded-lg p-4">
          <h3 class="text-sm font-semibold mb-3 text-gray-300">✨ 主要功能</h3>
          <div class="space-y-2 text-xs text-gray-400">
            <div class="flex items-center">
              <span class="mr-2">📚</span>
              課程與學術資訊查詢
            </div>
            <div class="flex items-center">
              <span class="mr-2">🏫</span>
              校園生活與設施指南
            </div>
            <div class="flex items-center">
              <span class="mr-2">📅</span>
              活動公告與重要通知
            </div>
            <div class="flex items-center">
              <span class="mr-2">🔍</span>
              即時網路資訊搜尋
            </div>
          </div>
        </div>
      </div>
    </aside>

    <!-- 右側：主要對話區域 -->
    <main class="main-content flex-1 flex flex-col ml-80 h-screen">
      <!-- 對話區域 -->
      <div class="flex-1 p-6 overflow-hidden">
        <div id="response-container" class="h-full bg-gray-800/50 backdrop-blur-sm rounded-lg shadow-2xl p-6 overflow-y-auto custom-scrollbar">
          <div id="welcome-message" class="flex flex-col items-center justify-center h-full text-center">
            <div class="text-6xl mb-6 animate-bounce">🤖</div>
            <h2 class="text-3xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              歡迎使用高科大 AI 助理！
            </h2>
            <p class="text-gray-300 mb-8 text-lg">我是您的智慧學習夥伴，隨時為您提供協助</p>

            <!-- 快速開始建議 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl w-full">
              <div class="bg-gray-700/30 backdrop-blur-sm p-4 rounded-lg hover:bg-gray-600/40 transition-all cursor-pointer group" onclick="quickStart('今天有什麼活動嗎？')">
                <div class="text-2xl mb-2 group-hover:scale-110 transition-transform">📅</div>
                <div class="font-semibold text-blue-300">校園活動</div>
                <div class="text-xs text-gray-400 mt-1">查詢今日活動安排</div>
              </div>
              <div class="bg-gray-700/30 backdrop-blur-sm p-4 rounded-lg hover:bg-gray-600/40 transition-all cursor-pointer group" onclick="quickStart('資管系的課程有哪些？')">
                <div class="text-2xl mb-2 group-hover:scale-110 transition-transform">📚</div>
                <div class="font-semibold text-green-300">課程查詢</div>
                <div class="text-xs text-gray-400 mt-1">了解課程資訊</div>
              </div>
              <div class="bg-gray-700/30 backdrop-blur-sm p-4 rounded-lg hover:bg-gray-600/40 transition-all cursor-pointer group" onclick="quickStart('圖書館在哪裡？開放時間？')">
                <div class="text-2xl mb-2 group-hover:scale-110 transition-transform">🏫</div>
                <div class="font-semibold text-purple-300">校園指南</div>
                <div class="text-xs text-gray-400 mt-1">設施位置與服務</div>
              </div>
              <div class="bg-gray-700/30 backdrop-blur-sm p-4 rounded-lg hover:bg-gray-600/40 transition-all cursor-pointer group" onclick="quickStart('如何申請學生證？')">
                <div class="text-2xl mb-2 group-hover:scale-110 transition-transform">❓</div>
                <div class="font-semibold text-yellow-300">常見問題</div>
                <div class="text-xs text-gray-400 mt-1">快速解答疑問</div>
              </div>
            </div>

            <div class="mt-8 text-sm text-gray-500">
              <p class="animate-pulse">💡 點擊上方卡片快速開始，或在下方輸入您的問題</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 固定底部輸入區 -->
      <div class="p-6 bg-gray-900/80 backdrop-blur-sm border-t border-gray-700">
        <div class="max-w-4xl mx-auto">
          <div class="flex gap-4">
            <textarea id="question"
              rows="1"
              placeholder="請輸入問題... (Shift+Enter 換行，Enter 送出)"
              class="flex-1 p-4 rounded-xl bg-gray-800 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 resize-none border border-gray-600 transition-all"
              onkeydown="handleKeyPress(event)" oninput="autoResize(this)"></textarea>

            <button id="send-button" onclick="send()"
              class="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600
                     text-white font-bold px-8 py-4 rounded-xl transition-all transform hover:scale-105 shadow-lg
                     disabled:opacity-50 disabled:cursor-not-allowed min-w-[120px] flex items-center justify-center">
              <span id="send-text">🚀 送出</span>
            </button>
          </div>

          <!-- 輸入提示 -->
          <div class="flex justify-between items-center mt-2 text-xs text-gray-500">
            <span>💡 支援 Markdown 格式 • 可搜尋即時資訊</span>
            <span>Enter 送出 • Shift+Enter 換行</span>
          </div>
        </div>
      </div>
    </main>
  </div>

  <!-- 自定義角色創建彈出框 -->
  <div id="customRoleModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-bold text-white">✨ 創建自定義角色</h3>
        <button id="closeCustomRoleModal" class="text-gray-400 hover:text-white">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <form id="customRoleForm" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-1">角色名稱 *</label>
          <input type="text" id="roleName" required
                 class="w-full p-3 rounded-lg bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                 placeholder="例如：客服專員、學術顧問、技術支援">
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-300 mb-1">組織名稱 *</label>
          <input type="text" id="organizationName" required
                 class="w-full p-3 rounded-lg bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                 placeholder="例如：ABC 科技公司、XYZ 大學">
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-300 mb-1">組織網域</label>
          <input type="text" id="organizationDomain"
                 class="w-full p-3 rounded-lg bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                 placeholder="例如：company.com、university.edu.tw">
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-300 mb-1">語調風格 *</label>
          <select id="toneStyle" required
                  class="w-full p-3 rounded-lg bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-purple-500">
            <option value="">請選擇語調風格</option>
            <option value="友善專業">友善專業</option>
            <option value="正式嚴謹">正式嚴謹</option>
            <option value="輕鬆親切">輕鬆親切</option>
            <option value="熱情活潑">熱情活潑</option>
            <option value="冷靜理性">冷靜理性</option>
            <option value="幽默風趣">幽默風趣</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-300 mb-1">特殊要求</label>
          <textarea id="specialRequirements" rows="3"
                    class="w-full p-3 rounded-lg bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="例如：需要處理技術問題、專注於學術諮詢、提供創意建議等"></textarea>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-300 mb-1">其他描述</label>
          <textarea id="description" rows="2"
                    class="w-full p-3 rounded-lg bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="任何其他想要補充的角色特色或要求"></textarea>
        </div>

        <div class="flex gap-3 pt-4">
          <button type="button" id="cancelCustomRole"
                  class="flex-1 py-3 px-4 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
            取消
          </button>
          <button type="submit" id="createCustomRole"
                  class="flex-1 py-3 px-4 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
            <span id="createRoleText">🚀 創建角色</span>
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- 角色創建進度彈出框 -->
  <div id="roleCreationProgress" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-gray-800 rounded-lg p-6 w-full max-w-lg mx-4">
      <div class="text-center">
        <div class="text-4xl mb-4">🧠</div>
        <h3 class="text-xl font-bold text-white mb-4">正在創建您的專屬角色</h3>
        <div class="bg-gray-700 rounded-lg p-4 mb-4">
          <div id="progressSteps" class="space-y-2 text-sm text-gray-300">
            <!-- 進度步驟會動態添加到這裡 -->
          </div>
        </div>
        <div class="flex items-center justify-center space-x-2">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-500"></div>
          <span class="text-gray-300">AI 正在分析和優化...</span>
        </div>
      </div>
    </div>
  </div>

  <!-- System Prompt 查看彈出框 -->
  <div id="systemPromptModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-gray-800 rounded-lg p-6 w-full max-w-5xl h-[90vh] overflow-hidden flex flex-col">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-bold text-white">📄 System Prompt 查看器</h3>
        <button id="closeSystemPromptModal" class="text-gray-400 hover:text-white">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-300 mb-2">選擇角色</label>
        <select id="promptRoleSelector" class="w-full p-3 rounded-lg bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
          <option value="">請選擇要查看的角色...</option>
        </select>
      </div>

      <div id="promptInfo" class="mb-4 hidden">
        <div class="bg-gray-700 rounded-lg p-4">
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-gray-400">角色名稱:</span>
              <span id="promptRoleName" class="text-white ml-2"></span>
            </div>
            <div>
              <span class="text-gray-400">組織:</span>
              <span id="promptOrgName" class="text-white ml-2"></span>
            </div>
            <div>
              <span class="text-gray-400">語調:</span>
              <span id="promptTone" class="text-white ml-2"></span>
            </div>
            <div>
              <span class="text-gray-400">長度:</span>
              <span id="promptLength" class="text-white ml-2"></span>
            </div>
          </div>
        </div>
      </div>

      <div class="flex-1 overflow-hidden min-h-0">
        <div class="bg-gray-900 rounded-lg p-4 h-full overflow-y-auto border border-gray-600" style="max-height: 100%;">
          <pre id="systemPromptContent" class="text-sm text-gray-300 whitespace-pre-wrap font-mono leading-relaxed block">
請選擇一個角色來查看其 System Prompt...
          </pre>
        </div>
      </div>

      <div class="mt-4 flex gap-3">
        <button id="copySystemPrompt" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50" disabled>
          📋 複製
        </button>
        <button id="exportSystemPrompt" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors disabled:opacity-50" disabled>
          💾 匯出
        </button>
        <button id="closeSystemPromptModalBtn" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
          關閉
        </button>
      </div>
    </div>
  </div>

<script>
// -------- 基本變數 --------
const responseContainer = document.getElementById("response-container");
let loadingInterval;
const user_id = crypto.randomUUID();        // 單一頁面唯一 ID
let isSending = false;
let customRoles = {}; // 儲存自定義角色

// -------- 自動調整輸入框高度 --------
function autoResize(textarea) {
  textarea.style.height = 'auto';
  textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
}



// -------- 快速開始功能 --------
function quickStart(question) {
  const textarea = document.getElementById('question');
  textarea.value = question;
  autoResize(textarea);
  textarea.focus();

  // 可選：自動發送
  // send();
}

// -------- 快捷鍵送出 Enter --------
function handleKeyPress(e){
  if(e.key==='Enter' && !e.shiftKey){
    e.preventDefault();
    send();
  }
}

// -------- 強制滾動到底部 --------
function scrollToBottom(smooth = false){
  // 使用多種方法確保滾動到底部
  const container = responseContainer;

  if (smooth) {
    container.scrollTo({
      top: container.scrollHeight,
      behavior: 'smooth'
    });
  } else {
    // 立即滾動
    container.scrollTop = container.scrollHeight;
  }

  // 備用方法：延遲滾動
  setTimeout(() => {
    container.scrollTop = container.scrollHeight;
  }, 50);

  // 再次確保滾動
  requestAnimationFrame(() => {
    container.scrollTop = container.scrollHeight;
  });
}

// -------- 送出訊息 (Rasa 優先) --------
async function send(){
  if (isSending) return;

  const textarea = document.getElementById("question");
  const sendButton = document.getElementById("send-button");
  const message = textarea.value.trim();

  if(!message){
    textarea.focus();
    return;
  }

  const model = document.getElementById("model").value;
  const mode = document.getElementById("mode").value;

  // 設置發送狀態
  isSending = true;
  sendButton.disabled = true;
  document.getElementById('send-text').innerHTML = '⏳ 發送中...';

  // 清空輸入框並重置高度
  textarea.value = "";
  textarea.style.height = 'auto';

  // 移除歡迎訊息
  const welcomeMsg = document.getElementById('welcome-message');
  if (welcomeMsg) {
    welcomeMsg.remove();
  }

  appendMessage("user", message);
  const aiBubble = appendMessage("ai", "");

  // 第一階段：嘗試 Rasa 回應
  startLoading(aiBubble, "Rasa");

  try {
    console.log("🤖 第一階段：嘗試 Rasa 回應");
    console.log("🤖 發送到 Rasa 的訊息:", message);

    // 先嘗試 Rasa
    const rasaResult = await tryRasaResponse(message);
    console.log("🤖 Rasa 處理結果:", rasaResult);

    if (rasaResult.shouldUseRasa) {
      // 使用 Rasa 回應
      stopLoading();
      aiBubble.innerHTML = `
        <div class="flex items-center mb-1 text-xs text-green-400">
          <span>Rasa</span>
        </div>
        <div class="ai-response-content">${rasaResult.response}</div>
      `;
      console.log("✅ 使用 Rasa 回應完成");
    } else {
      // 轉交 AI Agent
      console.log("🧠 第二階段：轉交 AI Agent 處理");
      stopLoading();

      // 顯示轉交訊息
      aiBubble.innerHTML = `
        <div class="flex items-center mb-1 text-xs text-yellow-400">
          <span>轉交 ${model}</span>
        </div>
      `;

      // 重新開始載入動畫
      setTimeout(() => {
        startLoading(aiBubble, `${model}`, true);
        handleAIAgentResponse(message, model, mode, aiBubble, rasaResult);
      }, 1000);

      return; // 提前返回，讓 AI 處理繼續
    }

  } catch (error) {
    console.error("❌ Rasa 處理失敗，直接使用 AI:", error);
    stopLoading();
    aiBubble.innerHTML = `
      <div class="flex items-center mb-1 text-xs text-orange-400">
        <span>使用 ${model}</span>
      </div>
    `;

    // 轉交 AI 處理
    setTimeout(() => {
      startLoading(aiBubble, `${model}`, true);
      handleAIAgentResponse(message, model, mode, aiBubble, null);
    }, 1000);

    return;
  } finally {
    // 重置發送狀態
    isSending = false;
    sendButton.disabled = false;
    document.getElementById('send-text').innerHTML = '🚀 送出';
    textarea.focus();
  }

  aiBubble.classList.add("fade-in");
  scrollToBottom();
}

// -------- Rasa 處理函數 --------
async function tryRasaResponse(message) {
  try {
    console.log("🤖 嘗試 Rasa 回應:", message);

    const response = await fetch("https://b225.54ucl.com/ui/rasa", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        message: message,
        user_id: user_id,
        model_id: "rasa"
      })
    });

    if (!response.ok) {
      throw new Error(`Rasa 服務錯誤 (${response.status})`);
    }

    const data = await response.json();
    console.log("🤖 Rasa 回應數據:", data);

    // 解析 Rasa 回應 (本地 Rasa 格式)
    if (Array.isArray(data) && data.length > 0) {
      const firstResponse = data[0];
      const text = data.map(item => item.text).join("\n");

      // 本地 Rasa 沒有直接提供 confidence，我們根據回應內容判斷
      let confidence = 0.5; // 默認中等信心度
      let intent = 'unknown';

      // 根據回應內容判斷信心度
      if (text && text.length > 20) {
        // 檢查是否是高信心度的回應
        if (text.includes('嗨，我是高科大') || text.includes('智慧助理') ||
            text.includes('你好') || text.includes('哈囉')) {
          confidence = 0.9; // 問候語高信心度
          intent = 'greet';
        } else if (text.includes('有什麼我可以幫你') || text.includes('需要什麼協助')) {
          confidence = 0.85; // 服務導向回應
          intent = 'help_offer';
        } else {
          confidence = 0.75; // 一般回應
        }
      }

      // 如果是 fallback 或通用回應，降低信心度
      if (text.includes('抱歉') || text.includes('不理解') || text.includes('不明白') ||
          text.includes('請問您需要查詢') || text.includes('請提供更詳細') ||
          text.includes('建議您') || text.includes('請針對您感興趣')) {
        confidence = 0.3;
        intent = 'fallback';
      }

      // 決策邏輯：信心度 >= 0.8 使用 Rasa
      const shouldUseRasa = confidence >= 0.8;

      return {
        shouldUseRasa: shouldUseRasa,
        confidence: confidence,
        intent: intent,
        response: text,
        rawData: data
      };
    } else {
      // 沒有有效回應
      return {
        shouldUseRasa: false,
        confidence: 0,
        intent: 'unknown',
        response: '抱歉，我無法理解您的問題。',
        rawData: data
      };
    }

  } catch (error) {
    console.error("🤖 Rasa 處理錯誤:", error);
    throw error;
  }
}

// -------- AI Agent 處理函數 --------
async function handleAIAgentResponse(message, model, mode, aiBubble, rasaContext) {
  try {
    console.log("🧠 AI Agent 處理:", { message, model, mode, rasaContext });

    const requestBody = {
      user_id: user_id,
      message: message,
      model_id: model,
      mode: mode,
      return_search_results: true
    };

    // 如果有 Rasa 上下文，添加到請求中
    if (rasaContext) {
      requestBody.rasa_context = {
        intent: rasaContext.intent,
        confidence: rasaContext.confidence,
        attempted_response: rasaContext.response
      };
    }

    const res = await fetch("/ui/chat", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestBody)
    });

    console.log("📡 AI Agent 響應:", res.status, res.statusText);
    if (!res.ok) throw new Error(`伺服器錯誤 (${res.status})`);

    stopLoading();

    // 添加 AI 回應標識
    const aiHeader = `
      <div class="flex items-center mb-1 text-xs text-blue-400">
        <span>${model}</span>
        ${rasaContext ? `<span class="ml-2 text-gray-400">• 基於 Rasa</span>` : ''}
      </div>
    `;

    aiBubble.innerHTML = aiHeader;

    const reader = res.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let full = "", searchShown = false;

    console.log("🔄 開始讀取 AI 流式響應...");

    while (true) {
      const { value, done } = await reader.read();
      if (done) {
        console.log("✅ AI 流式響應讀取完成");
        break;
      }
      const chunk = decoder.decode(value, { stream: true });
      console.log("📦 收到 AI chunk:", chunk.substring(0, 100) + (chunk.length > 100 ? "..." : ""));

      if (!searchShown && chunk.includes("---")) {
        const [jsonPart, textPart] = chunk.split("---");
        try {
          const data = JSON.parse(jsonPart.trim());
          if (data.searchResults?.length) {
            const r = data.searchResults[0];
            aiBubble.innerHTML += `
              <div class="bg-green-900 p-2 rounded mb-2 text-sm">
                🔍 新的搜尋結果
                <div class="text-xs text-gray-400">來源: ${r.url || '未知'}</div>
              </div>
              <div class="mt-2">${r.summary || ''}</div>`;
          }
        } catch { }
        full += textPart.trim();
        if (full) {
          if (!aiBubble.querySelector('.ai-text-content')) {
            aiBubble.innerHTML += `<div class="ai-text-content mt-4"></div>`;
          }
          aiBubble.querySelector('.ai-text-content').textContent = full;
        }
        searchShown = true;
      } else {
        full += chunk;
        if (searchShown) {
          if (!aiBubble.querySelector('.ai-text-content')) {
            aiBubble.innerHTML += `<div class="ai-text-content mt-4"></div>`;
          }
          aiBubble.querySelector('.ai-text-content').textContent = full;
        } else {
          // 保留 AI 標識，只更新內容部分
          const contentDiv = aiBubble.querySelector('.ai-response-content') ||
                           (() => {
                             const div = document.createElement('div');
                             div.className = 'ai-response-content';
                             aiBubble.appendChild(div);
                             return div;
                           })();
          contentDiv.textContent = full;
        }
      }

      // 每次更新內容後自動滾動
      setTimeout(() => scrollToBottom(), 100);
    }

  } catch (err) {
    console.error("❌ AI Agent 處理錯誤:", err);
    stopLoading();
    aiBubble.innerHTML += `
      <div class="mt-2 p-2 bg-red-900 border border-red-700 rounded">
        ❌ ${err.message}<br>
        <small class="text-gray-400">請稍後再試</small>
      </div>
    `;
  } finally {
    // 重置發送狀態
    isSending = false;
    document.getElementById("send-button").disabled = false;
    document.getElementById('send-text').innerHTML = '🚀 送出';
    document.getElementById("question").focus();
  }

  aiBubble.classList.add("fade-in");
  scrollToBottom();
}

// -------- UI 輔助 --------
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

function appendMessage(role, text){
  const wrap = document.createElement("div");
  wrap.className = `flex ${role==="user"?"justify-end":"justify-start"} fade-in mb-4`;
  const bubble = document.createElement("div");
  bubble.className = `max-w-lg px-3 py-2 rounded-2xl shadow-lg ${
    role==="user"?"bg-indigo-500":"bg-gray-700"
  } text-white whitespace-pre-wrap`;

  // 對於用戶消息使用textContent，對於AI回應使用innerHTML以支持格式化
  if(role === "user") {
    bubble.textContent = text;
  } else {
    bubble.innerHTML = text || "";
  }

  wrap.appendChild(bubble);
  responseContainer.appendChild(wrap);

  // 強制滾動到底部
  setTimeout(() => {
    responseContainer.scrollTop = responseContainer.scrollHeight;
  }, 50);

  return bubble;
}

function startLoading(el, message = "🧠 思考中", append = false){
  let dots = 0;

  // 如果是追加模式，保留現有內容
  const existingContent = append ? el.innerHTML : '';

  loadingInterval = setInterval(()=>{
    dots = (dots+1)%4;
    const loadingHtml = `
      <div class="flex items-center space-x-2">
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
        <span>${message}${"⋅".repeat(dots)}</span>
      </div>
    `;

    if (append) {
      // 追加模式：保留現有內容，添加載入動畫
      el.innerHTML = existingContent + `<div class="mt-2">${loadingHtml}</div>`;
    } else {
      // 替換模式：完全替換內容
      el.innerHTML = loadingHtml;
    }

    // 強制滾動到底部
    setTimeout(() => {
      responseContainer.scrollTop = responseContainer.scrollHeight;
    }, 50);
  }, 500);
}

function stopLoading(){
  clearInterval(loadingInterval);
}

// -------- System Prompt 查看功能 --------
function showSystemPromptModal() {
  document.getElementById('systemPromptModal').classList.remove('hidden');
  loadRolesForPromptViewer();
}

function hideSystemPromptModal() {
  document.getElementById('systemPromptModal').classList.add('hidden');
  document.getElementById('promptRoleSelector').value = '';
  hidePromptInfo();
}

async function loadRolesForPromptViewer() {
  try {
    const response = await fetch('/ui/roles');
    const roles = await response.json();

    const selector = document.getElementById('promptRoleSelector');

    // 清空現有選項（保留第一個預設選項）
    while (selector.children.length > 1) {
      selector.removeChild(selector.lastChild);
    }

    // 添加角色選項
    Object.entries(roles).forEach(([roleId, config]) => {
      const option = document.createElement('option');
      option.value = roleId;
      option.textContent = config.display_name || `${config.organization_name} - ${config.role_title}`;
      selector.appendChild(option);
    });

  } catch (error) {
    console.error('載入角色列表失敗:', error);
  }
}

async function loadSystemPrompt(roleId) {
  try {
    const response = await fetch(`/ui/roles/${roleId}/system-prompt`);
    const data = await response.json();

    if (data.error) {
      alert(`錯誤: ${data.error}`);
      return;
    }

    // 顯示角色資訊
    document.getElementById('promptRoleName').textContent = data.role_title;
    document.getElementById('promptOrgName').textContent = data.organization_name;
    document.getElementById('promptTone').textContent = data.tone;
    document.getElementById('promptLength').textContent = `${data.prompt_length} 字元`;

    // 顯示 System Prompt
    document.getElementById('systemPromptContent').textContent = data.system_prompt;

    // 顯示資訊區域和啟用按鈕
    document.getElementById('promptInfo').classList.remove('hidden');
    document.getElementById('copySystemPrompt').disabled = false;
    document.getElementById('exportSystemPrompt').disabled = false;

    // 儲存當前數據供其他功能使用
    window.currentPromptData = data;

  } catch (error) {
    console.error('載入 System Prompt 失敗:', error);
    alert('載入失敗，請稍後再試');
  }
}

function hidePromptInfo() {
  document.getElementById('promptInfo').classList.add('hidden');
  document.getElementById('systemPromptContent').textContent = '請選擇一個角色來查看其 System Prompt...';
  document.getElementById('copySystemPrompt').disabled = true;
  document.getElementById('exportSystemPrompt').disabled = true;
  window.currentPromptData = null;
}

async function copySystemPromptToClipboard() {
  if (!window.currentPromptData) return;

  try {
    await navigator.clipboard.writeText(window.currentPromptData.system_prompt);

    // 臨時改變按鈕文字
    const button = document.getElementById('copySystemPrompt');
    const originalText = button.textContent;
    button.textContent = '✅ 已複製';
    button.classList.add('bg-green-600');

    setTimeout(() => {
      button.textContent = originalText;
      button.classList.remove('bg-green-600');
    }, 2000);

  } catch (error) {
    console.error('複製失敗:', error);
    alert('複製失敗，請手動選取文字複製');
  }
}

function exportSystemPromptToFile() {
  if (!window.currentPromptData) return;

  const data = window.currentPromptData;
  const content = `角色 ID: ${data.role_id}
角色名稱: ${data.role_name}
組織: ${data.organization_name}
語調: ${data.tone}
長度: ${data.prompt_length} 字元
匯出時間: ${new Date().toLocaleString()}

${'='.repeat(80)}

${data.system_prompt}`;

  // 創建下載連結
  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `system_prompt_${data.role_id}.txt`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  // 臨時改變按鈕文字
  const button = document.getElementById('exportSystemPrompt');
  const originalText = button.textContent;
  button.textContent = '✅ 已匯出';
  button.classList.add('bg-green-600');

  setTimeout(() => {
    button.textContent = originalText;
    button.classList.remove('bg-green-600');
  }, 2000);
}

// -------- 監聽容器變化，自動滾動 --------
const observer = new MutationObserver((mutations) => {
  // 檢查是否有內容變化
  let shouldScroll = false;
  mutations.forEach(mutation => {
    if (mutation.type === 'childList' || mutation.type === 'characterData') {
      shouldScroll = true;
    }
  });

  if (shouldScroll) {
    setTimeout(() => {
      responseContainer.scrollTop = responseContainer.scrollHeight;
    }, 100);
  }
});

// 當頁面載入完成後開始監聽
document.addEventListener('DOMContentLoaded', () => {
  observer.observe(responseContainer, {
    childList: true,
    subtree: true,
    characterData: true
  });

  // 讓輸入框獲得焦點
  document.getElementById('question').focus();

  // 初始滾動到底部
  setTimeout(() => {
    responseContainer.scrollTop = responseContainer.scrollHeight;
  }, 100);
});




// -------- 自定義角色功能 --------
async function loadCustomRoles() {
  try {
    const response = await fetch('/ui/roles');
    if (response.ok) {
      customRoles = await response.json();
      updateRoleSelector();
    }
  } catch (error) {
    console.error('載入自定義角色失敗:', error);
  }
}

function updateRoleSelector() {
  const modeSelect = document.getElementById('mode');

  // 移除舊的自定義角色選項（保留預設選項和創建選項）
  const options = Array.from(modeSelect.options);
  options.forEach(option => {
    if (option.value.startsWith('custom_') && option.value !== 'custom_create') {
      option.remove();
    }
  });

  // 添加新的自定義角色選項
  const createOption = modeSelect.querySelector('option[value="custom_create"]');

  Object.entries(customRoles).forEach(([roleId, config]) => {
    if (roleId.startsWith('custom_')) {
      const option = document.createElement('option');
      option.value = roleId;
      option.textContent = `🎭 ${config.display_name || config.organization_name + ' - ' + config.role_title}`;
      modeSelect.insertBefore(option, createOption);
    }
  });
}

function showCustomRoleModal() {
  document.getElementById('customRoleModal').classList.remove('hidden');
}

function hideCustomRoleModal() {
  document.getElementById('customRoleModal').classList.add('hidden');
  document.getElementById('customRoleForm').reset();
}

function showRoleCreationProgress() {
  document.getElementById('roleCreationProgress').classList.remove('hidden');
  document.getElementById('progressSteps').innerHTML = '';
}

function hideRoleCreationProgress() {
  document.getElementById('roleCreationProgress').classList.add('hidden');
}

function addProgressStep(message, isComplete = false) {
  const progressSteps = document.getElementById('progressSteps');
  const step = document.createElement('div');
  step.className = `flex items-center space-x-2 ${isComplete ? 'text-green-400' : 'text-gray-300'}`;
  step.innerHTML = `
    <span>${isComplete ? '✅' : '⏳'}</span>
    <span>${message}</span>
  `;
  progressSteps.appendChild(step);

  // 滾動到最新步驟
  progressSteps.scrollTop = progressSteps.scrollHeight;
}

async function createCustomRole(formData) {
  try {
    showRoleCreationProgress();
    addProgressStep('開始分析角色需求...');

    const response = await fetch('/ui/create-role', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    });

    if (!response.ok) {
      throw new Error(`伺服器錯誤 (${response.status})`);
    }

    const result = await response.json();

    // 顯示創建過程
    if (result.process_log) {
      for (const log of result.process_log) {
        addProgressStep(log, log.includes('✅'));
        await new Promise(resolve => setTimeout(resolve, 500)); // 延遲顯示
      }
    }

    if (result.success) {
      addProgressStep(`🎉 角色創建成功！最終分數: ${result.final_score}/10`, true);

      // 等待一下讓用戶看到成功訊息
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 重新載入角色列表
      await loadCustomRoles();

      // 自動選擇新創建的角色
      const modeSelect = document.getElementById('mode');
      modeSelect.value = result.role_id;

      hideRoleCreationProgress();
      hideCustomRoleModal();

      // 顯示成功通知
      alert(`🎉 角色「${formData.role_name}」創建成功！\n評分: ${result.final_score}/10\n迭代次數: ${result.iterations}`);

    } else {
      addProgressStep(`❌ 創建失敗: ${result.error}`, false);
      setTimeout(() => {
        hideRoleCreationProgress();
        alert(`創建失敗: ${result.error}`);
      }, 2000);
    }

  } catch (error) {
    console.error('創建角色失敗:', error);
    addProgressStep(`❌ 網路錯誤: ${error.message}`, false);
    setTimeout(() => {
      hideRoleCreationProgress();
      alert(`創建失敗: ${error.message}`);
    }, 2000);
  }
}

// 事件監聽器
document.addEventListener('DOMContentLoaded', function() {
  // 載入自定義角色
  loadCustomRoles();

  // 角色選擇變更
  document.getElementById('mode').addEventListener('change', function(e) {
    if (e.target.value === 'custom_create') {
      showCustomRoleModal();
      // 重置選擇到之前的值
      setTimeout(() => {
        if (e.target.selectedIndex > 0) {
          e.target.selectedIndex = e.target.selectedIndex - 1;
        }
      }, 100);
    }
  });

  // 自定義角色表單事件
  document.getElementById('closeCustomRoleModal').addEventListener('click', hideCustomRoleModal);
  document.getElementById('cancelCustomRole').addEventListener('click', hideCustomRoleModal);

  document.getElementById('customRoleForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = {
      role_name: document.getElementById('roleName').value.trim(),
      organization_name: document.getElementById('organizationName').value.trim(),
      organization_domain: document.getElementById('organizationDomain').value.trim(),
      tone_style: document.getElementById('toneStyle').value,
      special_requirements: document.getElementById('specialRequirements').value.trim(),
      description: document.getElementById('description').value.trim()
    };

    // 基本驗證
    if (!formData.role_name || !formData.organization_name || !formData.tone_style) {
      alert('請填寫所有必填欄位（標記 * 的欄位）');
      return;
    }

    await createCustomRole(formData);
  });

  // System Prompt 查看相關事件
  document.getElementById('closeSystemPromptModal').addEventListener('click', hideSystemPromptModal);
  document.getElementById('closeSystemPromptModalBtn').addEventListener('click', hideSystemPromptModal);

  document.getElementById('promptRoleSelector').addEventListener('change', async function(e) {
    const roleId = e.target.value;
    if (roleId) {
      await loadSystemPrompt(roleId);
    } else {
      hidePromptInfo();
    }
  });

  document.getElementById('copySystemPrompt').addEventListener('click', copySystemPromptToClipboard);
  document.getElementById('exportSystemPrompt').addEventListener('click', exportSystemPromptToFile);

  // 角色管理按鈕
  document.getElementById('refresh-roles').addEventListener('click', loadCustomRoles);
  document.getElementById('view-system-prompt').addEventListener('click', showSystemPromptModal);
});

</script>



</body>
</html>
