#!/usr/bin/env python3
"""
測試 PDF 後端處理功能
"""

import asyncio
import aiohttp
import os
from pathlib import Path

async def test_pdf_upload():
    """測試 PDF 上傳和處理功能"""
    print("🧪 測試 PDF 後端處理功能")
    print("=" * 60)
    
    # 測試用的 PDF 檔案路徑（您需要準備一個測試 PDF）
    test_pdf_path = "test.pdf"
    
    # 檢查測試檔案是否存在
    if not os.path.exists(test_pdf_path):
        print("❌ 測試 PDF 檔案不存在")
        print("請準備一個名為 'test.pdf' 的測試檔案")
        return False
    
    # 測試參數
    test_data = {
        'user_id': 'test-user-123',
        'message': '請幫我分析這個 PDF 檔案的內容',
        'model_id': 'gpt-4o-mini',
        'mode': 'default',
        'return_search_results': 'true'
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            # 準備檔案上傳
            with open(test_pdf_path, 'rb') as pdf_file:
                data = aiohttp.FormData()
                
                # 添加表單欄位
                for key, value in test_data.items():
                    data.add_field(key, value)
                
                # 添加檔案
                data.add_field('pdf_file', pdf_file, 
                             filename='test.pdf', 
                             content_type='application/pdf')
                
                print("📤 發送 PDF 上傳請求...")
                
                # 發送請求
                async with session.post(
                    'http://localhost:8088/ui/chat_pdf',
                    data=data
                ) as response:
                    print(f"📡 響應狀態: {response.status}")
                    
                    if response.status == 200:
                        print("✅ 請求成功，開始讀取流式響應...")
                        
                        # 讀取流式響應
                        async for chunk in response.content.iter_chunked(1024):
                            if chunk:
                                text = chunk.decode('utf-8', errors='ignore')
                                print(f"📦 收到: {text[:100]}...")
                                
                    else:
                        error_text = await response.text()
                        print(f"❌ 請求失敗: {error_text}")
                        return False
                        
    except aiohttp.ClientConnectorError:
        print("❌ 無法連接到服務器，請確認服務器正在運行 (http://localhost:8088)")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False
    
    print("✅ PDF 測試完成")
    return True

async def test_pdf_extraction():
    """測試 PDF 文字提取功能"""
    print("\n🔍 測試 PDF 文字提取功能")
    print("-" * 40)
    
    # 導入 PDF 提取函數
    try:
        import sys
        sys.path.append('.')
        from web_app import extract_pdf_text
        
        test_pdf_path = "test.pdf"
        
        if not os.path.exists(test_pdf_path):
            print("❌ 測試 PDF 檔案不存在")
            return False
        
        # 讀取檔案
        with open(test_pdf_path, 'rb') as f:
            pdf_content = f.read()
        
        print(f"📄 測試檔案: {test_pdf_path}")
        print(f"📊 檔案大小: {len(pdf_content)} bytes")
        
        # 提取文字
        extracted_text = await extract_pdf_text(pdf_content, test_pdf_path)
        
        print(f"📝 提取結果長度: {len(extracted_text)} 字符")
        print(f"📖 前 200 字符預覽:")
        print("-" * 40)
        print(extracted_text[:200])
        print("-" * 40)
        
        return True
        
    except ImportError as e:
        print(f"❌ 導入錯誤: {e}")
        return False
    except Exception as e:
        print(f"❌ 提取測試失敗: {e}")
        return False

def create_test_pdf():
    """創建一個簡單的測試 PDF（如果沒有的話）"""
    print("\n📝 創建測試 PDF 檔案")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        filename = "test.pdf"
        c = canvas.Canvas(filename, pagesize=letter)
        
        # 添加一些測試內容
        c.drawString(100, 750, "測試 PDF 檔案")
        c.drawString(100, 730, "這是一個用於測試 PDF 處理功能的檔案")
        c.drawString(100, 710, "")
        c.drawString(100, 690, "內容包括：")
        c.drawString(120, 670, "1. 中文文字測試")
        c.drawString(120, 650, "2. 英文文字測試 - English text test")
        c.drawString(120, 630, "3. 數字測試 - 123456789")
        c.drawString(120, 610, "4. 特殊符號測試 - !@#$%^&*()")
        
        c.showPage()
        c.save()
        
        print(f"✅ 測試 PDF 檔案已創建: {filename}")
        return True
        
    except ImportError:
        print("⚠️ reportlab 未安裝，無法創建測試 PDF")
        print("請手動準備一個名為 'test.pdf' 的測試檔案")
        return False
    except Exception as e:
        print(f"❌ 創建測試 PDF 失敗: {e}")
        return False

async def main():
    """主測試函數"""
    print("🚀 PDF 後端功能測試")
    print("=" * 80)
    
    # 檢查是否有測試檔案
    if not os.path.exists("test.pdf"):
        print("📄 未找到測試 PDF 檔案，嘗試創建...")
        create_test_pdf()
    
    # 測試 PDF 文字提取
    extraction_success = await test_pdf_extraction()
    
    if extraction_success:
        # 測試完整的 PDF 上傳流程
        upload_success = await test_pdf_upload()
        
        if upload_success:
            print("\n🎉 所有測試通過！")
            print("\n💡 PDF 功能已就緒，您可以：")
            print("   1. 在前端上傳 PDF 檔案")
            print("   2. 輸入關於 PDF 的問題")
            print("   3. AI 會分析 PDF 內容並回答")
        else:
            print("\n⚠️ 上傳測試失敗，請檢查服務器狀態")
    else:
        print("\n❌ PDF 文字提取測試失敗")
        print("請確認已安裝 PDF 處理套件：")
        print("   pip install pdfplumber")
        print("   或")
        print("   pip install PyPDF2")

if __name__ == "__main__":
    asyncio.run(main())
