#!/usr/bin/env python3
"""
測試修復後的 Rasa stories 配置
"""

import asyncio
import aiohttp
import json

async def test_address_query_flow():
    """測試地址查詢流程"""
    
    print("🧪 測試地址查詢流程")
    print("=" * 60)
    
    # 測試案例：應該詢問校區
    test_message = "高科的地址"
    user_id = f"test_stories_{int(asyncio.get_event_loop().time())}"
    
    print(f"📤 第一輪：{test_message}")
    print("🎯 期望：詢問校區")
    
    try:
        async with aiohttp.ClientSession() as session:
            # 第一輪：問地址
            async with session.post("https://b225.54ucl.com/ui/rasa", json={
                "message": test_message,
                "user_id": user_id,
                "model_id": "rasa"
            }) as response:
                
                if response.status == 200:
                    data = await response.json()
                    print("📥 Rasa 回應:")
                    
                    if isinstance(data, list) and len(data) > 0:
                        for item in data:
                            text = item.get('text', '')
                            print(f"  🤖 {text}")
                            
                            # 檢查是否詢問校區
                            if '你想查詢哪個校區' in text or '校區名稱' in text:
                                print("  ✅ 正確：詢問校區")
                                
                                # 等待一下，然後進行第二輪
                                await asyncio.sleep(2)
                                
                                # 第二輪：回答校區
                                print(f"\n📤 第二輪：楠梓校區")
                                print("🎯 期望：回答楠梓校區地址")
                                
                                async with session.post("https://b225.54ucl.com/ui/rasa", json={
                                    "message": "楠梓校區",
                                    "user_id": user_id,  # 相同 user_id 保持對話狀態
                                    "model_id": "rasa"
                                }) as response2:
                                    
                                    if response2.status == 200:
                                        data2 = await response2.json()
                                        print("📥 Rasa 回應:")
                                        
                                        if isinstance(data2, list) and len(data2) > 0:
                                            for item2 in data2:
                                                text2 = item2.get('text', '')
                                                print(f"  🤖 {text2}")
                                                
                                                if '楠梓校區的地址是' in text2:
                                                    print("  ✅ 正確：回答楠梓校區地址")
                                                else:
                                                    print("  ❌ 錯誤：沒有回答地址")
                                        else:
                                            print("  ❌ 沒有回應")
                                    else:
                                        print(f"  ❌ 第二輪錯誤: {response2.status}")
                                
                                return True
                            
                            elif '地址是' in text:
                                print("  ❌ 錯誤：直接回答地址，沒有詢問校區")
                                return False
                            else:
                                print("  ⚠️ 其他回應")
                    else:
                        print("  ❌ 沒有回應")
                        return False
                else:
                    print(f"❌ 錯誤: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

async def test_direct_campus_query():
    """測試直接指定校區的查詢"""
    
    print("\n🧪 測試直接指定校區")
    print("=" * 60)
    
    test_cases = [
        "楠梓校區的地址",
        "建工校區在哪",
        "第一校區地址"
    ]
    
    for i, message in enumerate(test_cases, 1):
        print(f"\n📤 測試 {i}: {message}")
        print("🎯 期望：直接回答地址")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post("https://b225.54ucl.com/ui/rasa", json={
                    "message": message,
                    "user_id": f"test_direct_{i}",
                    "model_id": "rasa"
                }) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        print("📥 Rasa 回應:")
                        
                        if isinstance(data, list) and len(data) > 0:
                            for item in data:
                                text = item.get('text', '')
                                print(f"  🤖 {text}")
                                
                                if '地址是' in text and '高雄市' in text:
                                    print("  ✅ 正確：直接回答地址")
                                elif '你想查詢哪個校區' in text:
                                    print("  ❌ 錯誤：不應該詢問校區")
                                else:
                                    print("  ⚠️ 其他回應")
                        else:
                            print("  ❌ 沒有回應")
                    else:
                        print(f"  ❌ 錯誤: {response.status}")
                        
        except Exception as e:
            print(f"  ❌ 測試失敗: {e}")

async def main():
    """主測試函數"""
    print("🚀 測試修復後的 Rasa Stories")
    print("=" * 80)
    
    # 1. 測試需要詢問校區的流程
    success = await test_address_query_flow()
    
    # 2. 測試直接指定校區的查詢
    await test_direct_campus_query()
    
    print("\n🎯 測試總結")
    print("=" * 80)
    
    if success:
        print("✅ 多輪對話測試通過")
    else:
        print("❌ 多輪對話測試失敗")
    
    print("\n💡 如果測試失敗：")
    print("1. 需要重新訓練 Rasa 模型")
    print("2. 檢查 stories 配置是否正確")
    print("3. 確認 NLU 訓練資料充足")
    print("4. 重新啟動 Rasa 服務")

if __name__ == "__main__":
    asyncio.run(main())
