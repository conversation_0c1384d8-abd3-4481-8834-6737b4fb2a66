<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDF 路由測試</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white p-8">
  <div class="max-w-4xl mx-auto">
    <h1 class="text-3xl font-bold mb-8">🔍 PDF 路由測試</h1>
    
    <div class="bg-gray-800 rounded-xl p-6 mb-6">
      <h2 class="text-xl font-semibold mb-4">測試場景</h2>
      
      <div class="space-y-4">
        <!-- 場景 1: 純文字訊息 -->
        <div class="bg-gray-700 p-4 rounded-lg">
          <h3 class="font-medium mb-2">🔤 場景 1: 純文字訊息</h3>
          <p class="text-sm text-gray-300 mb-3">應該發送到: <code class="bg-gray-600 px-2 py-1 rounded">/ui/chat</code></p>
          <button onclick="testTextOnly()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-sm">
            測試純文字
          </button>
        </div>
        
        <!-- 場景 2: PDF + 文字 -->
        <div class="bg-gray-700 p-4 rounded-lg">
          <h3 class="font-medium mb-2">📄 場景 2: PDF + 文字訊息</h3>
          <p class="text-sm text-gray-300 mb-3">應該發送到: <code class="bg-gray-600 px-2 py-1 rounded">/ui/chat_pdf</code></p>
          <input type="file" id="test-pdf" accept=".pdf" class="mb-2 text-sm">
          <br>
          <button onclick="testPDFWithText()" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded text-sm">
            測試 PDF + 文字
          </button>
        </div>
        
        <!-- 場景 3: 只有 PDF -->
        <div class="bg-gray-700 p-4 rounded-lg">
          <h3 class="font-medium mb-2">📎 場景 3: 只有 PDF</h3>
          <p class="text-sm text-gray-300 mb-3">應該發送到: <code class="bg-gray-600 px-2 py-1 rounded">/ui/chat_pdf</code></p>
          <button onclick="testPDFOnly()" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded text-sm">
            測試純 PDF
          </button>
        </div>
      </div>
    </div>
    
    <!-- 測試結果 -->
    <div class="bg-gray-800 rounded-xl p-6">
      <h2 class="text-xl font-semibold mb-4">📊 測試結果</h2>
      <div id="test-results" class="bg-gray-900 p-4 rounded-lg min-h-[200px] font-mono text-sm">
        <div class="text-gray-400">等待測試...</div>
      </div>
      <button onclick="clearResults()" class="mt-4 bg-red-600 hover:bg-red-700 px-4 py-2 rounded text-sm">
        清除結果
      </button>
    </div>
  </div>

  <script>
    function logResult(message) {
      const results = document.getElementById('test-results');
      const timestamp = new Date().toLocaleTimeString();
      results.innerHTML += `<div class="mb-1">[${timestamp}] ${message}</div>`;
      results.scrollTop = results.scrollHeight;
    }

    function clearResults() {
      document.getElementById('test-results').innerHTML = '<div class="text-gray-400">等待測試...</div>';
    }

    // 模擬 handleAIAgentResponse 函數的路由選擇邏輯
    function simulateRouting(message, pdfFile = null) {
      logResult(`📤 輸入: 訊息="${message}", PDF=${!!pdfFile ? '有' : '無'}`);
      
      if (pdfFile) {
        logResult(`📄 檢測到 PDF 檔案: ${pdfFile.name} (${pdfFile.size} bytes)`);
        logResult(`✅ 路由選擇: /ui/chat_pdf`);
        logResult(`🔄 跳過 Rasa，直接轉交 AI Agent`);
        
        // 模擬 FormData 創建
        const formData = new FormData();
        formData.append('user_id', 'test-user');
        formData.append('message', message || '請分析這個 PDF 檔案');
        formData.append('model_id', 'test-model');
        formData.append('mode', 'default');
        formData.append('return_search_results', 'true');
        formData.append('pdf_file', pdfFile);
        
        logResult(`📦 FormData 已創建，包含 PDF 檔案`);
        
      } else {
        logResult(`📝 純文字訊息`);
        logResult(`✅ 路由選擇: /ui/chat`);
        logResult(`🤖 先嘗試 Rasa 回應`);
        
        // 模擬 JSON 創建
        const jsonData = {
          user_id: 'test-user',
          message: message,
          model_id: 'test-model',
          mode: 'default',
          return_search_results: true
        };
        
        logResult(`📦 JSON 已創建: ${JSON.stringify(jsonData, null, 2)}`);
      }
      
      logResult(`─────────────────────────────────────`);
    }

    function testTextOnly() {
      logResult(`🔤 測試場景 1: 純文字訊息`);
      simulateRouting("這是一個測試訊息", null);
    }

    function testPDFWithText() {
      const fileInput = document.getElementById('test-pdf');
      const file = fileInput.files[0];
      
      if (!file) {
        logResult(`❌ 請先選擇 PDF 檔案`);
        return;
      }
      
      logResult(`📄 測試場景 2: PDF + 文字訊息`);
      simulateRouting("請分析這個 PDF 檔案的內容", file);
    }

    function testPDFOnly() {
      const fileInput = document.getElementById('test-pdf');
      const file = fileInput.files[0];
      
      if (!file) {
        logResult(`❌ 請先選擇 PDF 檔案`);
        return;
      }
      
      logResult(`📎 測試場景 3: 只有 PDF`);
      simulateRouting("", file);
    }

    // 頁面載入時的說明
    window.onload = function() {
      logResult(`🚀 PDF 路由測試工具已載入`);
      logResult(`💡 這個工具模擬前端的路由選擇邏輯`);
      logResult(`📋 測試不同場景下的請求路由`);
      logResult(`─────────────────────────────────────`);
    };
  </script>
</body>
</html>
