version: "3.1"

intents:
  - greet
  - ask_department
  - ask_office_location
  - ask_admission
  - goodbye
  - affirm
  - deny
  - mood_great
  - mood_unhappy
  - bot_challenge

entities:
  - department
  - campus

actions:
  - action_fallback_handle

forms:
  campus_form:
    required_slots:
      - campus
  
slots:
  campus:
    type: text
    influence_conversation: true
    mappings:
      - type: from_entity
        entity: campus
      - type: from_text
        conditions:
          - active_loop: campus_form
            requested_slot: campus

  department:
    type: text
    influence_conversation: true
    mappings:
      - type: from_entity
        entity: department

responses:
  utter_greet:
  - text: "嗨，我是高科大的智慧助理，有什麼我可以幫你的？"

  utter_cheer_up:
  - text: "Here is something to cheer you up:"
    image: "https://i.imgur.com/nGF1K8f.jpg"

  utter_did_that_help:
  - text: "Did that help you?"

  utter_happy:
  - text: "Great, carry on!"

  utter_goodbye:
  - text: "祝你有個美好的一天~"

  utter_ask_department:
      - text: "你想了解哪個系呢？"

  utter_iamabot:
  - text: "我是高科大的智慧助理，是由Rasa核心驅動"

  utter_ask_campus:
    - text: "你想查詢哪個校區呢？請告訴我校區名稱，例如：楠梓校區、建工校區等。"
    
  utter_reply_campus:
      - condition:
          - type: slot
            name: campus
            value: 楠梓校區
        text: "楠梓校區的地址是：高雄市楠梓區海專路142號"
      - condition:
          - type: slot
            name: campus
            value: 建工校區
        text: "建工校區的地址是：高雄市三民區建工路415號"
      - condition:
          - type: slot
            name: campus
            value: 第一校區
        text: "第一校區的地址是：高雄市燕巢區大學路1號"
      - condition:
          - type: slot
            name: campus
            value: 燕巢校區
        text: "燕巢校區的地址是：高雄市燕巢區深中路58號"
      - condition:
          - type: slot
            name: campus
            value: 旗津校區
        text: "旗津校區的地址是：高雄市旗津區中洲三路482號"
      - text: "很抱歉，我查不到該校區的地址，可以再確認一次嗎？"

session_config:
  session_expiration_time: 60
  carry_over_slots_to_new_session: true
