#!/usr/bin/env python3
"""
簡單測試 Rasa 服務回應
"""

import asyncio
import aiohttp
import json

async def test_rasa_direct():
    """直接測試 Rasa 服務的不同問題"""
    
    test_cases = [
        {
            "name": "問候語測試",
            "message": "你好",
            "expected_confidence": "high"
        },
        {
            "name": "活動查詢",
            "message": "今天有什麼活動嗎？",
            "expected_confidence": "low"
        },
        {
            "name": "課程查詢", 
            "message": "高科大有哪些課程？",
            "expected_confidence": "low"
        }
    ]
    
    print("🤖 測試 Rasa 服務回應")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 測試 {i}: {test_case['name']}")
        print("-" * 40)
        print(f"📤 問題: {test_case['message']}")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post("https://b225.54ucl.com/ui/rasa", json={
                    "message": test_case['message'],
                    "user_id": f"test_user_{i}",
                    "model_id": "rasa"
                }) as response:
                    
                    print(f"📊 狀態碼: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        print("📥 Rasa 原始回應:")
                        print(json.dumps(data, ensure_ascii=False, indent=2))
                        
                        # 模擬前端邏輯判斷信心度
                        if isinstance(data, list) and len(data) > 0:
                            text = " ".join([item.get('text', '') for item in data])
                            print(f"📝 回應文字: {text}")
                            
                            # 信心度判斷邏輯 (複製前端邏輯)
                            confidence = 0.5
                            intent = 'unknown'
                            
                            if text and len(text) > 20:
                                if ('嗨，我是高科大' in text or '智慧助理' in text or 
                                    '你好' in text or '哈囉' in text):
                                    confidence = 0.9
                                    intent = 'greet'
                                elif ('有什麼我可以幫你' in text or '需要什麼協助' in text):
                                    confidence = 0.85
                                    intent = 'help_offer'
                                else:
                                    confidence = 0.75
                            
                            if ('抱歉' in text or '不理解' in text or '不明白' in text or
                                '請問您需要查詢' in text or '請提供更詳細' in text or
                                '建議您' in text or '請針對您感興趣' in text):
                                confidence = 0.3
                                intent = 'fallback'
                            
                            should_use_rasa = confidence >= 0.8
                            
                            print(f"⭐ 計算信心度: {confidence:.2f}")
                            print(f"🎯 識別意圖: {intent}")
                            print(f"🤖 使用 Rasa: {'✅ 是' if should_use_rasa else '❌ 否，轉交 AI'}")
                            
                            # 驗證預期
                            expected = test_case['expected_confidence']
                            if expected == "high" and should_use_rasa:
                                print("🎉 測試通過！")
                            elif expected == "low" and not should_use_rasa:
                                print("🎉 測試通過！")
                            else:
                                print(f"⚠️ 測試結果與預期不符：期望 {expected}，實際 {'high' if should_use_rasa else 'low'}")
                        
                    else:
                        error_text = await response.text()
                        print(f"❌ Rasa 服務錯誤: {error_text}")
                        
        except Exception as e:
            print(f"❌ 連接錯誤: {e}")
        
        print("\n" + "=" * 60)

async def main():
    await test_rasa_direct()
    
    print("\n💡 總結:")
    print("- 問候語應該由 Rasa 處理 (信心度 >= 0.8)")
    print("- 複雜查詢應該轉交 AI (信心度 < 0.8)")
    print("- 前端會根據信心度自動分流")

if __name__ == "__main__":
    asyncio.run(main())
