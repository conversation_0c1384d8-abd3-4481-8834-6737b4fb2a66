# monkey_patch_tool_call.py
from semantic_kernel.contents.utils.finish_reason import FinishReason

# 將 'tool_call' 補上為 FinishReason.TOOL_CALLS 的別名
if "tool_call" not in FinishReason._value2member_map_:
    FinishReason._value2member_map_["tool_call"] = FinishReason.TOOL_CALLS

# web_app.py
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Dict

import asyncio, json, re
from loguru import logger

from agent_factory import build_agent, get_instructions_for_model
from plugins.logger_utils import configure_logging, log_timing

from semantic_kernel.kernel import KernelArguments
from semantic_kernel.connectors.ai.function_choice_behavior import Function<PERSON>hoiceBehavior
from semantic_kernel.connectors.ai.prompt_execution_settings import PromptExecutionSettings
from semantic_kernel.connectors.ai.open_ai.prompt_execution_settings.azure_chat_prompt_execution_settings import (
    AzureChatPromptExecutionSettings,
    OpenAIChatPromptExecutionSettings,
)


configure_logging("INFO")

# ─────────────────────────── FastAPI 及 CORS ───────────────────────────
app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],          # 開發階段放行
    allow_methods=["*"],
    allow_headers=["*"],
)

# ─────────────────────────── 全域物件 ──────────────────────────────
kernel = None
default_agent = None      # JSON搜尋機器人 Agent
nkust_agent = None        # 高科AI助手 Agent
deepseek_agent = None     # DeepSeek專用 Agent
threads: Dict[str, any] = {}

# 全域變數：新增一個 cfg_agents 映射
cfg_agents: Dict[str, any] = {}
# ─────────────────────────── 請求 schema ─────────────────────────────
class ChatRequest(BaseModel):
    user_id: str
    message: str
    model_id: str
    mode: str = "default"                   # Agent 模式：default, nkust_assistant, deepseek
    rasa_context: dict = None               # Rasa 上下文資訊

class CreateRoleRequest(BaseModel):
    role_name: str
    organization_name: str
    organization_domain: str = ""
    tone_style: str
    special_requirements: str = ""
    description: str = ""
# ─────────────────────────── 啟動 ────────────────────────────────────
@app.on_event("startup")
@log_timing("build_agent")
async def startup():
    global kernel, default_agent, nkust_agent, deepseek_agent
    from semantic_kernel.agents import ChatCompletionAgent

    # 初始化 kernel
    _, kernel = await build_agent()

    # 創建三個不同模式的 Agent
    default_agent = ChatCompletionAgent(
        kernel=kernel,
        name="default_agent",
        instructions=get_instructions_for_model("default")
    )

    nkust_agent = ChatCompletionAgent(
        kernel=kernel,
        name="nkust_agent",
        instructions=get_instructions_for_model("nkust_assistant")
    )

    deepseek_agent = ChatCompletionAgent(
        kernel=kernel,
        name="deepseek_agent",
        instructions=get_instructions_for_model("deepseek")
    )

    print("✅ 三個 Agent 啟動完成（DeepSeek 使用原生 Function Calling）")

# ─────────────────────────── 主要路由 ──────────────────────────────
@app.post("/ui/chat")
@log_timing()
async def chat(req: ChatRequest):
    global kernel, threads, default_agent, nkust_agent, deepseek_agent

    # 取得 / 建立 thread
    thread = threads.get(req.user_id)
    if thread is None:
        logger.info(f"👤 新對話 {req.user_id}")
        threads[req.user_id] = None
        thread = None

    try:
        # ── 1. 根據 mode 和 model_id 選擇 Agent ──
        if req.mode.startswith("custom_"):
            # 處理自定義角色
            from plugins.role_creator import RoleCreator
            from semantic_kernel.agents import ChatCompletionAgent

            role_creator = RoleCreator(kernel)
            available_roles = role_creator.get_available_roles()

            if req.mode not in available_roles:
                return {"error": f"❌ 自定義角色不存在：{req.mode}"}

            if req.mode not in cfg_agents:
                logger.info(f"🧠 初始化自定義角色: {req.mode}")
                role_config = available_roles[req.mode]
                system_prompt = role_config.get("system_prompt", "")

                if not system_prompt:
                    # 如果沒有 system_prompt，使用基本配置生成
                    from plugins.prompt_generator import PromptGenerator
                    generator = PromptGenerator(kernel)
                    system_prompt = generator._create_basic_system_prompt({
                        "role_name": role_config.get("role_title", "助理"),
                        "organization_name": role_config.get("organization_name", "組織"),
                        "organization_domain": role_config.get("organization_domain", ""),
                        "tone_style": role_config.get("tone", "友善專業"),
                        "special_requirements": "",
                        "description": ""
                    })

                cfg_agents[req.mode] = ChatCompletionAgent(
                    kernel=kernel,
                    name=f"custom_{req.mode}",
                    instructions=system_prompt
                )

            agent = cfg_agents[req.mode]
            source = f"自定義角色 ({available_roles[req.mode].get('display_name', req.mode)})"

        elif req.mode.startswith("cfg:"):
            from plugins.agent_roles_config import ROLE_CONFIGS
            from semantic_kernel.agents import ChatCompletionAgent
            role_key = req.mode[4:]

            if role_key not in ROLE_CONFIGS:
                return {"error": f"❌ 無此角色：{role_key}"}

            if role_key not in cfg_agents:
                logger.info(f"🧠 初始化 cfg_agent: {role_key}")
                cfg_agents[role_key] = ChatCompletionAgent(
                    kernel=kernel,
                    name=f"cfg_{role_key}",
                    instructions=get_instructions_for_model(req.mode, req.rasa_context)
                )

            agent = cfg_agents[role_key]
            source = f"CFG-Agent ({role_key})"

        elif req.model_id == "deepseek" or req.mode == "deepseek":
            # 為 DeepSeek 創建帶有 Rasa 上下文的 agent
            if req.rasa_context:
                agent = ChatCompletionAgent(
                    kernel=kernel,
                    name="deepseek_agent_with_context",
                    instructions=get_instructions_for_model("deepseek", req.rasa_context)
                )
            else:
                agent = deepseek_agent
            source = "DeepSeek-Agent"

        elif req.mode == "nkust_assistant":
            # 為 NKUST 創建帶有 Rasa 上下文的 agent
            if req.rasa_context:
                agent = ChatCompletionAgent(
                    kernel=kernel,
                    name="nkust_agent_with_context",
                    instructions=get_instructions_for_model("nkust_assistant", req.rasa_context)
                )
            else:
                agent = nkust_agent
            source = "NKUST-Assistant"

        else:
            # 為 Default 創建帶有 Rasa 上下文的 agent
            if req.rasa_context:
                agent = ChatCompletionAgent(
                    kernel=kernel,
                    name="default_agent_with_context",
                    instructions=get_instructions_for_model("default", req.rasa_context)
                )
            else:
                agent = default_agent
            source = "Default-JSON"

        logger.info(f"🎯 使用 Agent：{source}")

        # ── 2. 建立 PromptExecutionSettings ──
        if req.model_id == "o3-mini":
            pe_settings = OpenAIChatPromptExecutionSettings(
                service_id=req.model_id,
                max_tokens=4000,
                temperature=0.7,
                function_choice_behavior=FunctionChoiceBehavior.Auto()
            )
        elif req.model_id == "deepseek":
            # DeepSeek-R1-0528 支援原生 function calling
            pe_settings = PromptExecutionSettings(
                service_id=req.model_id,
                function_choice_behavior=FunctionChoiceBehavior.Auto(),
            )
        else:
            pe_settings = PromptExecutionSettings(
                service_id=req.model_id,
                function_choice_behavior=FunctionChoiceBehavior.Auto(),
            )
        arguments = KernelArguments(settings=pe_settings)

        # ── 3. 記錄 log ──
        logger.info(
            f"📩 {req.model_id} – {req.user_id} : {req.message[:50]} "
            f"(thread: {'有' if thread else '無'})"
        )

        # ── 4. 先跑標題搜尋 (websearch plugin) ──
        try:
            search_results = await agent.kernel.plugins["websearch"].search_titles_only(
                req.message
            )
            logger.info(f"🔍 搜尋結果 {len(search_results)} 筆")
        except Exception as e:
            logger.error(f"搜尋失敗: {e}")
            search_results = []

        #── 5. 建立串流回傳 ── 出現tool_call問題
        async def stream_generator():
            nonlocal thread
            total_chars, full_response = 0, ""

            # (a) 先把搜尋結果 JSON 傳給前端
            if search_results:
                data = {
                    "searchResults": [
                        {
                            "url": str(r.get("url", "")),
                            "title": str(r.get("title", "")),
                            "summary": str(r.get("summary", "")),
                        }
                        for r in search_results
                    ]
                }
                yield json.dumps(data, ensure_ascii=False) + "\n---\n"

            # (b) 主模型串流
            logger.info(f"🔄 開始 {req.model_id} 串流回應...")
            response_count = 0

            async for resp in agent.invoke_stream(
                messages=req.message, thread=thread, arguments=arguments
            ):
                response_count += 1
                msg = getattr(resp, "message", resp)

                logger.debug(f"📦 收到回應 #{response_count}: role={getattr(msg, 'role', 'unknown')}, content_length={len(getattr(msg, 'content', '')) if hasattr(msg, 'content') else 0}")

                    # 攔截工具調用，處理 tool_call -> tool_calls 的相容性修補
                if hasattr(msg, "metadata"):
                    finish_reason = msg.metadata.get("finish_reason")
                    if finish_reason == "tool_call":
                        logger.warning("⚠️ 修補 DeepSeek 回傳格式：tool_call -> TOOL_CALLS")
                        msg.metadata["finish_reason"] = "TOOL_CALLS"

                if msg.role == "assistant" and msg.content:
                    total_chars += len(msg.content)
                    full_response += msg.content
                    yield msg.content
                    await asyncio.sleep(0)

                # thread 追蹤
                new_thread = getattr(resp, "thread", thread)
                if new_thread is not thread:
                    thread = new_thread
                    threads[req.user_id] = thread

            # (c) DeepSeek-R1-0528 使用原生 function calling，不需要特殊後處理
            # 原生 function calling 會自動處理工具調用和結果整合

            logger.info(
                f"🔽 Stream 完成 – {req.user_id} 用 {req.model_id}，共 {response_count} 個回應，{total_chars} 字"
            )

        return StreamingResponse(stream_generator(), media_type="text/plain; charset=utf-8")

    except Exception as e:
        logger.error(f"❌ chat error: {e}")
        return StreamingResponse(
            content=f"Error processing request: {e}",
            media_type="text/plain; charset=utf-8",
        )

# ─────────────────────────── 角色管理 API ──────────────────────────────
@app.post("/ui/create-role")
@log_timing()
async def create_role(req: CreateRoleRequest):
    """創建自定義角色"""
    try:
        from plugins.role_creator import RoleCreator

        # 創建角色創建器
        role_creator = RoleCreator(kernel)

        # 轉換請求為字典
        user_input = {
            "role_name": req.role_name,
            "organization_name": req.organization_name,
            "organization_domain": req.organization_domain,
            "tone_style": req.tone_style,
            "special_requirements": req.special_requirements,
            "description": req.description
        }

        logger.info(f"開始創建角色: {req.role_name} @ {req.organization_name}")

        # 創建角色
        result = await role_creator.create_custom_role(user_input)

        logger.info(f"角色創建完成: {result['success']}, 分數: {result['final_score']}")

        return result

    except Exception as e:
        logger.error(f"創建角色 API 錯誤: {e}")
        return {
            "success": False,
            "error": str(e),
            "role_id": None,
            "config": None,
            "final_score": 0.0,
            "iterations": 0,
            "process_log": [f"API 錯誤: {str(e)}"]
        }

@app.get("/ui/roles")
@log_timing()
async def get_roles():
    """獲取所有可用角色"""
    try:
        from plugins.role_creator import RoleCreator

        role_creator = RoleCreator(kernel)
        roles = role_creator.get_available_roles()

        logger.info(f"返回 {len(roles)} 個角色配置")
        return roles

    except Exception as e:
        logger.error(f"獲取角色列表錯誤: {e}")
        return {}

@app.delete("/ui/roles/{role_id}")
@log_timing()
async def delete_role(role_id: str):
    """刪除指定角色"""
    try:
        from plugins.role_creator import RoleCreator

        role_creator = RoleCreator(kernel)
        success = role_creator.delete_role(role_id)

        if success:
            logger.info(f"角色 {role_id} 已刪除")
            return {"success": True, "message": f"角色 {role_id} 已刪除"}
        else:
            return {"success": False, "message": f"角色 {role_id} 不存在或刪除失敗"}

    except Exception as e:
        logger.error(f"刪除角色錯誤: {e}")
        return {"success": False, "message": str(e)}

@app.get("/ui/learning-insights")
@log_timing()
async def get_learning_insights():
    """獲取系統學習洞察"""
    try:
        from plugins.role_creator import RoleCreator

        role_creator = RoleCreator(kernel)
        insights = role_creator.get_learning_insights()
        evolution_summary = role_creator.get_evolution_summary()

        return {
            "insights": insights,
            "evolution_summary": evolution_summary,
            "timestamp": "2025-07-04T12:40:16"
        }

    except Exception as e:
        logger.error(f"獲取學習洞察錯誤: {e}")
        return {"error": str(e)}

@app.get("/ui/roles/{role_id}/system-prompt")
@log_timing()
async def get_system_prompt(role_id: str):
    """獲取指定角色的 System Prompt"""
    try:
        from plugins.role_creator import RoleCreator

        role_creator = RoleCreator(kernel)
        roles = role_creator.get_available_roles()

        if role_id not in roles:
            return {"error": f"角色 {role_id} 不存在"}

        config = roles[role_id]
        system_prompt = config.get("system_prompt", "")

        if not system_prompt:
            return {"error": f"角色 {role_id} 沒有 System Prompt"}

        return {
            "role_id": role_id,
            "role_name": config.get("display_name", f"{config.get('organization_name', '')} - {config.get('role_title', '')}"),
            "system_prompt": system_prompt,
            "prompt_length": len(system_prompt),
            "organization_name": config.get("organization_name", ""),
            "role_title": config.get("role_title", ""),
            "tone": config.get("tone", ""),
            "timestamp": "2025-07-04T12:40:16"
        }

    except Exception as e:
        logger.error(f"獲取 System Prompt 錯誤: {e}")
        return {"error": str(e)}

# ─────────────────────────── 靜態頁面 ───────────────────────────────
app.mount("/ui", StaticFiles(directory="static", html=True), name="static")

# ─────────────────────────── 啟動服務器 ───────────────────────────────
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8088)
