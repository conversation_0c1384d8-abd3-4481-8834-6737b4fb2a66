#!/usr/bin/env python3
"""
System Prompt 查看工具
"""

import json
import os
from typing import Dict, Any

def load_configs() -> Dict[str, Any]:
    """載入組織配置"""
    config_path = "plugins/organization_configs.json"
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 載入配置失敗: {e}")
        return {}

def list_all_roles():
    """列出所有角色"""
    configs = load_configs()
    
    print("📋 所有可用角色:")
    print("=" * 60)
    
    for i, (role_id, config) in enumerate(configs.items(), 1):
        org_name = config.get('organization_name', '未知組織')
        role_title = config.get('role_title', '未知角色')
        display_name = config.get('display_name', f"{org_name} - {role_title}")
        
        # 檢查是否有 system_prompt
        has_system_prompt = 'system_prompt' in config
        prompt_length = len(config.get('system_prompt', '')) if has_system_prompt else 0
        
        print(f"{i:2d}. {role_id}")
        print(f"    📛 名稱: {display_name}")
        print(f"    📝 System Prompt: {'✅' if has_system_prompt else '❌'} ({prompt_length} 字元)")
        print()

def view_system_prompt(role_id: str):
    """查看指定角色的 System Prompt"""
    configs = load_configs()
    
    if role_id not in configs:
        print(f"❌ 找不到角色: {role_id}")
        return
    
    config = configs[role_id]
    system_prompt = config.get('system_prompt', '')
    
    if not system_prompt:
        print(f"❌ 角色 {role_id} 沒有 System Prompt")
        return
    
    org_name = config.get('organization_name', '未知組織')
    role_title = config.get('role_title', '未知角色')
    display_name = config.get('display_name', f"{org_name} - {role_title}")
    
    print(f"🎭 角色: {display_name}")
    print(f"🆔 ID: {role_id}")
    print(f"📏 長度: {len(system_prompt)} 字元")
    print("=" * 80)
    print("📄 System Prompt:")
    print("-" * 80)
    print(system_prompt)
    print("-" * 80)

def search_prompts(keyword: str):
    """搜尋包含關鍵字的 System Prompt"""
    configs = load_configs()
    
    print(f"🔍 搜尋關鍵字: '{keyword}'")
    print("=" * 60)
    
    found_count = 0
    
    for role_id, config in configs.items():
        system_prompt = config.get('system_prompt', '')
        if keyword.lower() in system_prompt.lower():
            found_count += 1
            
            org_name = config.get('organization_name', '未知組織')
            role_title = config.get('role_title', '未知角色')
            display_name = config.get('display_name', f"{org_name} - {role_title}")
            
            print(f"✅ {role_id}: {display_name}")
            
            # 顯示包含關鍵字的片段
            lines = system_prompt.split('\n')
            for i, line in enumerate(lines):
                if keyword.lower() in line.lower():
                    print(f"   第 {i+1} 行: {line.strip()}")
            print()
    
    if found_count == 0:
        print("❌ 沒有找到包含該關鍵字的 System Prompt")
    else:
        print(f"📊 共找到 {found_count} 個匹配的角色")

def compare_prompts(role_id1: str, role_id2: str):
    """比較兩個角色的 System Prompt"""
    configs = load_configs()
    
    if role_id1 not in configs:
        print(f"❌ 找不到角色: {role_id1}")
        return
    
    if role_id2 not in configs:
        print(f"❌ 找不到角色: {role_id2}")
        return
    
    config1 = configs[role_id1]
    config2 = configs[role_id2]
    
    prompt1 = config1.get('system_prompt', '')
    prompt2 = config2.get('system_prompt', '')
    
    if not prompt1 or not prompt2:
        print("❌ 其中一個角色沒有 System Prompt")
        return
    
    name1 = config1.get('display_name', role_id1)
    name2 = config2.get('display_name', role_id2)
    
    print(f"🔄 比較 System Prompt")
    print(f"角色 A: {name1}")
    print(f"角色 B: {name2}")
    print("=" * 80)
    
    print(f"📏 長度比較:")
    print(f"  角色 A: {len(prompt1)} 字元")
    print(f"  角色 B: {len(prompt2)} 字元")
    print()
    
    # 簡單的相似度分析
    lines1 = set(prompt1.split('\n'))
    lines2 = set(prompt2.split('\n'))
    
    common_lines = lines1.intersection(lines2)
    unique_to_1 = lines1 - lines2
    unique_to_2 = lines2 - lines1
    
    print(f"📊 結構分析:")
    print(f"  共同行數: {len(common_lines)}")
    print(f"  角色 A 獨有: {len(unique_to_1)}")
    print(f"  角色 B 獨有: {len(unique_to_2)}")
    
    if len(common_lines) > 0:
        similarity = len(common_lines) / max(len(lines1), len(lines2)) * 100
        print(f"  相似度: {similarity:.1f}%")

def export_prompt(role_id: str, output_file: str = None):
    """匯出 System Prompt 到文件"""
    configs = load_configs()
    
    if role_id not in configs:
        print(f"❌ 找不到角色: {role_id}")
        return
    
    config = configs[role_id]
    system_prompt = config.get('system_prompt', '')
    
    if not system_prompt:
        print(f"❌ 角色 {role_id} 沒有 System Prompt")
        return
    
    if not output_file:
        output_file = f"system_prompt_{role_id}.txt"
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"角色 ID: {role_id}\n")
            f.write(f"角色名稱: {config.get('display_name', '未知')}\n")
            f.write(f"匯出時間: {__import__('datetime').datetime.now().isoformat()}\n")
            f.write("=" * 80 + "\n")
            f.write(system_prompt)
        
        print(f"✅ System Prompt 已匯出到: {output_file}")
        
    except Exception as e:
        print(f"❌ 匯出失敗: {e}")

def main():
    """主函數"""
    while True:
        print("\n🎭 System Prompt 查看工具")
        print("=" * 40)
        print("1. 📋 列出所有角色")
        print("2. 👁️  查看 System Prompt")
        print("3. 🔍 搜尋 System Prompt")
        print("4. 🔄 比較兩個 System Prompt")
        print("5. 💾 匯出 System Prompt")
        print("0. 🚪 退出")
        print()
        
        choice = input("請選擇功能 (0-5): ").strip()
        
        if choice == "0":
            print("👋 再見！")
            break
        elif choice == "1":
            list_all_roles()
        elif choice == "2":
            role_id = input("請輸入角色 ID: ").strip()
            if role_id:
                view_system_prompt(role_id)
        elif choice == "3":
            keyword = input("請輸入搜尋關鍵字: ").strip()
            if keyword:
                search_prompts(keyword)
        elif choice == "4":
            role_id1 = input("請輸入第一個角色 ID: ").strip()
            role_id2 = input("請輸入第二個角色 ID: ").strip()
            if role_id1 and role_id2:
                compare_prompts(role_id1, role_id2)
        elif choice == "5":
            role_id = input("請輸入角色 ID: ").strip()
            output_file = input("請輸入輸出檔名 (留空使用預設): ").strip()
            if role_id:
                export_prompt(role_id, output_file if output_file else None)
        else:
            print("❌ 無效的選擇，請重新輸入")

if __name__ == "__main__":
    main()
