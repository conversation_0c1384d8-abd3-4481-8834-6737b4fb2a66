#!/usr/bin/env python3
"""
測試角色創建系統
"""

import asyncio
import json
from plugins.prompt_generator import PromptGenerator
from plugins.prompt_evaluator import PromptEvaluator
from plugins.role_creator import RoleCreator

async def test_prompt_generator():
    """測試 Prompt 生成器"""
    print("🧪 測試 Prompt 生成器")
    print("=" * 50)
    
    generator = PromptGenerator()
    
    # 測試用戶輸入
    user_input = {
        "role_name": "客服專員",
        "organization_name": "ABC 科技公司",
        "organization_domain": "abc-tech.com",
        "tone_style": "友善專業",
        "special_requirements": "需要處理技術問題和產品諮詢",
        "description": "專門協助客戶解決技術問題"
    }
    
    print("📋 用戶輸入:")
    for key, value in user_input.items():
        print(f"  {key}: {value}")
    
    print("\n🔄 生成 system prompt...")
    system_prompt = await generator.generate_system_prompt(user_input)
    
    print("\n📄 生成的 System Prompt:")
    print("-" * 30)
    print(system_prompt)
    print("-" * 30)
    
    return system_prompt, user_input

async def test_prompt_evaluator(system_prompt, user_input):
    """測試 Prompt 評估器"""
    print("\n🧪 測試 Prompt 評估器")
    print("=" * 50)
    
    evaluator = PromptEvaluator()
    
    print("🔍 評估 system prompt...")
    score, suggestions = evaluator.evaluate_system_prompt(system_prompt, user_input)
    
    print(f"\n📊 評估結果:")
    print(f"  分數: {score}/10")
    print(f"  建議數量: {len(suggestions)}")
    
    if suggestions:
        print("\n💡 改進建議:")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"  {i}. {suggestion}")
    else:
        print("\n✅ 沒有改進建議，品質良好！")
    
    return score, suggestions

async def test_role_creator():
    """測試角色創建器"""
    print("\n🧪 測試角色創建器")
    print("=" * 50)
    
    role_creator = RoleCreator()
    
    # 測試用戶輸入
    user_input = {
        "role_name": "學術顧問",
        "organization_name": "未來大學",
        "organization_domain": "future-uni.edu.tw",
        "tone_style": "專業親切",
        "special_requirements": "專門協助學生選課和學術規劃",
        "description": "提供學術建議和課程指導"
    }
    
    print("📋 創建角色:")
    for key, value in user_input.items():
        print(f"  {key}: {value}")
    
    print("\n🔄 開始創建角色...")
    result = await role_creator.create_custom_role(user_input)
    
    print(f"\n📊 創建結果:")
    print(f"  成功: {result['success']}")
    print(f"  角色 ID: {result['role_id']}")
    print(f"  最終分數: {result['final_score']}/10")
    print(f"  迭代次數: {result['iterations']}")
    
    if result['error']:
        print(f"  錯誤: {result['error']}")
    
    print(f"\n📝 創建過程:")
    for log in result['process_log']:
        print(f"  - {log}")
    
    return result

async def test_role_management():
    """測試角色管理功能"""
    print("\n🧪 測試角色管理")
    print("=" * 50)
    
    role_creator = RoleCreator()
    
    # 獲取所有角色
    print("📋 獲取所有角色...")
    roles = role_creator.get_available_roles()
    
    print(f"找到 {len(roles)} 個角色:")
    for role_id, config in roles.items():
        display_name = config.get('display_name', f"{config.get('organization_name', '未知')} - {config.get('role_title', '未知')}")
        print(f"  - {role_id}: {display_name}")
    
    return roles

def test_config_structure():
    """測試配置結構"""
    print("\n🧪 測試配置結構")
    print("=" * 50)
    
    generator = PromptGenerator()
    
    user_input = {
        "role_name": "測試助理",
        "organization_name": "測試公司",
        "organization_domain": "test.com",
        "tone_style": "友善",
        "special_requirements": "測試功能",
        "description": "這是測試"
    }
    
    config = generator._create_basic_config(user_input)
    
    print("📋 基本配置結構:")
    print(json.dumps(config, ensure_ascii=False, indent=2))
    
    return config

async def main():
    """主測試函數"""
    print("🚀 開始測試角色創建系統")
    print("=" * 60)
    
    try:
        # 1. 測試配置結構
        test_config_structure()
        
        # 2. 測試 Prompt 生成器
        system_prompt, user_input = await test_prompt_generator()
        
        # 3. 測試 Prompt 評估器
        score, suggestions = await test_prompt_evaluator(system_prompt, user_input)
        
        # 4. 測試角色創建器
        creation_result = await test_role_creator()
        
        # 5. 測試角色管理
        roles = await test_role_management()
        
        print("\n🎉 所有測試完成！")
        print("=" * 60)
        
        # 總結
        print("\n📊 測試總結:")
        print(f"  - Prompt 生成: ✅")
        print(f"  - Prompt 評估: ✅ (分數: {score}/10)")
        print(f"  - 角色創建: {'✅' if creation_result['success'] else '❌'}")
        print(f"  - 角色管理: ✅ (共 {len(roles)} 個角色)")
        
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
