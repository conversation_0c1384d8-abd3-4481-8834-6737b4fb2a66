# plugins/qdrant_faq_plugin.py

import anyio
from mcp.server.stdio import stdio_server
from semantic_kernel import Kern<PERSON>
from semantic_kernel.functions import kernel_function
from qdrant_client import QdrantClient
from langchain_openai import AzureOpenAIEmbeddings
from langchain_qdrant import QdrantVectorStore

class QdrantFAQ:
    def __init__(self):
        # 🔥 直接寫你的 Azure 參數
        qdrant_url = "http://localhost:6333"
        collection_name = "faq"  # 你的 Qdrant Collection 名稱
        
        # Azure OpenAI 的參數
        api_key = "F0c4EqxP4tVvUCBzMAgo8hE45dPVsJrNLlh7zMIbVSVysq9AC1kTJQQJ99BCACHYHv6XJ3w3AAAAACOGtWKN"
        azure_endpoint = "https://ai-c1121181119171ai093822801868.openai.azure.com/"
        azure_deployment = "text-embedding-ada-002"
        api_version = "2024-02-15-preview"  # 或你設定的 API 版本
        
        self.client = QdrantClient(url=qdrant_url)
        self.embeddings = AzureOpenAIEmbeddings(
            azure_endpoint=azure_endpoint,
            deployment=azure_deployment,
            api_version=api_version,
            api_key=api_key,
            model="text-embedding-ada-002",  # 這個 model name 可以寫，Azure有用就好
        )
        self.vector_db = QdrantVectorStore(
            client=self.client,
            collection_name=collection_name,
            embedding=self.embeddings,
        )

    @kernel_function(name="faq_lookup", description="根據問題向量比對常見問題資料庫並回傳最相關的回答")
    async def faq_lookup(self, input: str) -> str:
        try:
            results = self.vector_db.similarity_search(input, k=5)
            if not results:
                return "(❌ 沒有找到相關的常見問題)"

            output = []
            for idx, doc in enumerate(results):
                meta = doc.metadata or {}
                question = meta.get('question', '未知問題')
                output.append(f"{idx+1}. ❓ {question}\n📘 {doc.page_content}")

            return "\n\n".join(output)
        except Exception as e:
            return f"(RAG 搜尋失敗: {e})"

def run() -> None:
    kernel = Kernel()
    faq_plugin = QdrantFAQ()
    kernel.add_function("faq", faq_plugin.faq_lookup)

    server = kernel.as_mcp_server(server_name="faq_lookup")

    async def handle_stdin():
        print("✅ Qdrant FAQ MCP Server 正在啟動...")
        async with stdio_server() as (read_stream, write_stream):
            await server.run(read_stream, write_stream, server.create_initialization_options())

    anyio.run(handle_stdin)

if __name__ == "__main__":
    run()



# # plugins/faq_lookup_plugin.py

# import os
# import json
# from difflib import get_close_matches
# from semantic_kernel.functions import kernel_function

# BASE_DIR = os.path.dirname(os.path.abspath(__file__))
# FAQ_FILE = os.path.join(BASE_DIR, "faq.json")  # FAQ 檔案放同目錄或自訂

# @kernel_function(name="faq_lookup", description="根據常見問題模糊比對資料庫並回傳回答")
# async def faq_lookup(input: str) -> str:
#     """從 faq.json 中模糊查詢最接近的問題，命中就回傳答案，否則回應無結果。"""
#     if not os.path.exists(FAQ_FILE):
#         return "❌ FAQ 資料庫不存在。"

#     with open(FAQ_FILE, "r", encoding="utf-8") as f:
#         faq_data = json.load(f)

#     questions = list(faq_data.keys())
#     matches = get_close_matches(input.strip(), questions, n=3, cutoff=0.6)
#     if matches:
#         print(f"[FAQ Match] 使用者問題：{input.strip()} -> 匹配：{matches}")
#         response = []
#         for q in matches:
#             response.append(f"❓ {q}\n📘 {faq_data[q]}")

#         return "\n\n".join(response)
#     else:
#         return "🔍 沒有找到相符的常見問題。"
