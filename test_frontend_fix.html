<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>前端修復測試</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white p-8">
  <div class="max-w-4xl mx-auto">
    <h1 class="text-3xl font-bold mb-6">🔧 前端顯示修復測試</h1>
    
    <div class="bg-gray-800 rounded-lg p-6 mb-6">
      <h2 class="text-xl font-semibold mb-4">測試流式響應顯示</h2>
      
      <div class="mb-4">
        <input type="text" id="testMessage" placeholder="輸入測試消息..." 
               class="w-full p-3 rounded bg-gray-700 text-white">
      </div>
      
      <button onclick="testStreamResponse()" 
              class="bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded">
        測試流式響應
      </button>
      
      <button onclick="testSearchAndText()" 
              class="bg-green-600 hover:bg-green-700 px-6 py-2 rounded ml-4">
        測試搜索+文本
      </button>
      
      <button onclick="clearTest()" 
              class="bg-red-600 hover:bg-red-700 px-6 py-2 rounded ml-4">
        清除測試
      </button>
    </div>
    
    <div id="testContainer" class="bg-gray-800 rounded-lg p-6 min-h-[400px]">
      <div class="text-gray-400 text-center">點擊上方按鈕開始測試...</div>
    </div>
    
    <div class="mt-6 bg-gray-800 rounded-lg p-4">
      <h3 class="text-lg font-semibold mb-2">🐛 調試信息</h3>
      <div id="debugInfo" class="text-sm text-gray-300 font-mono">
        等待測試開始...
      </div>
    </div>
  </div>

<script>
const testContainer = document.getElementById('testContainer');
const debugInfo = document.getElementById('debugInfo');

function log(message) {
  console.log(message);
  debugInfo.innerHTML += `<div>${new Date().toLocaleTimeString()}: ${message}</div>`;
  debugInfo.scrollTop = debugInfo.scrollHeight;
}

function clearTest() {
  testContainer.innerHTML = '<div class="text-gray-400 text-center">點擊上方按鈕開始測試...</div>';
  debugInfo.innerHTML = '等待測試開始...';
}

function createTestBubble() {
  const wrap = document.createElement("div");
  wrap.className = "flex justify-start fade-in mb-4";
  const bubble = document.createElement("div");
  bubble.className = "max-w-lg p-3 rounded-2xl shadow-lg bg-gray-700 text-white whitespace-pre-wrap";
  wrap.appendChild(bubble);
  testContainer.appendChild(wrap);
  return bubble;
}

async function testStreamResponse() {
  log("🚀 開始測試純文本流式響應");
  clearTest();
  
  const bubble = createTestBubble();
  let full = "";
  
  // 模擬流式文本響應
  const testText = "這是一個測試的流式響應。我會逐字顯示這段文字，來測試前端是否能正確處理流式更新。每個字符都會被逐步添加到顯示區域中。";
  
  for(let i = 0; i < testText.length; i++) {
    full += testText[i];
    bubble.textContent = full;
    log(`📝 更新文本長度: ${full.length}`);
    await new Promise(resolve => setTimeout(resolve, 50));
  }
  
  log("✅ 純文本流式響應測試完成");
}

async function testSearchAndText() {
  log("🔍 開始測試搜索結果+文本流式響應");
  clearTest();
  
  const bubble = createTestBubble();
  
  // 第一步：顯示搜索結果
  log("📊 添加搜索結果");
  bubble.innerHTML = `
    <div class="bg-green-900 p-2 rounded mb-2 text-sm">
      🔍 新的搜尋結果
      <div class="text-xs text-gray-400">來源: https://example.com</div>
    </div>
    <div class="mt-2">這是搜索結果的摘要內容</div>`;
  
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 第二步：添加文本內容區域
  log("📝 添加文本內容區域");
  if(!bubble.querySelector('.ai-text-content')) {
    bubble.innerHTML += `<div class="ai-text-content mt-4"></div>`;
  }
  
  // 第三步：流式更新文本內容
  const testText = "這是AI的回應內容。我會在保持搜索結果顯示的同時，逐步更新這段文字。";
  let full = "";
  
  for(let i = 0; i < testText.length; i++) {
    full += testText[i];
    bubble.querySelector('.ai-text-content').textContent = full;
    log(`📝 更新AI文本長度: ${full.length}`);
    await new Promise(resolve => setTimeout(resolve, 50));
  }
  
  log("✅ 搜索結果+文本流式響應測試完成");
}
</script>

</body>
</html>
