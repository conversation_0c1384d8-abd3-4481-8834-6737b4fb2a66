import os
import asyncio
import html
import httpx
import trafilatura
from trafilatura.settings import use_config
from dotenv import load_dotenv
from mcp.server.stdio import stdio_server
from semantic_kernel import Kernel
from semantic_kernel.functions import kernel_function
from typing import Annotated
from loguru import logger
from logger_utils import configure_logging, log_timing
configure_logging("INFO")
from urllib.parse import quote, unquote
import time
import random
import hashlib
from datetime import datetime

# Qdrant 相關導入
try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams, PointStruct
    from sentence_transformers import SentenceTransformer
    QDRANT_AVAILABLE = True
except ImportError:
    QDRANT_AVAILABLE = False
    logger.warning("⚠️ Qdrant 或 SentenceTransformers 未安裝，將跳過向量化存儲")

GATEWAY = "https://b225.54ucl.com"

# --- 環境變數載入 ---
load_dotenv()

# --- 常數與初始化 ---
HEADERS = {
    "User-Agent": (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
        "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    )
}
SEARXNG_URL = os.environ.get("SEARXNG_URL", "http://localhost:4000").rstrip("/")
MAX_CHARS_PER_PAGE = 300  # 摘要最大長度
MAX_FULL_TEXT_LENGTH = 5000  # 全文最大長度
MAX_RESULTS = 10  # 搜尋最多10筆

# --- Qdrant 配置 ---
QDRANT_HOST = os.environ.get("QDRANT_HOST", "localhost")
QDRANT_PORT = int(os.environ.get("QDRANT_PORT", "6333"))
QDRANT_COLLECTION = "websearch_cache"
EMBEDDING_MODEL = "all-MiniLM-L6-v2"

# --- 全域變數 ---
qdrant_client = None
embedding_model = None

def to_proxy(url: str) -> str:
    return f"{GATEWAY}/ui/proxy/?target={url}"

# --- Qdrant 初始化 ---
def init_qdrant():
    """初始化 Qdrant 客戶端和 embedding 模型"""
    global qdrant_client, embedding_model

    if not QDRANT_AVAILABLE:
        logger.warning("⚠️ Qdrant 不可用，跳過初始化")
        return False

    try:
        # 初始化 Qdrant 客戶端
        qdrant_client = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)

        # 初始化 embedding 模型
        embedding_model = SentenceTransformer(EMBEDDING_MODEL)

        # 確保集合存在
        try:
            qdrant_client.get_collection(QDRANT_COLLECTION)
            logger.info(f"✅ Qdrant 集合 '{QDRANT_COLLECTION}' 已存在")
        except:
            # 創建新集合
            qdrant_client.create_collection(
                collection_name=QDRANT_COLLECTION,
                vectors_config=VectorParams(
                    size=embedding_model.get_sentence_embedding_dimension(),
                    distance=Distance.COSINE
                )
            )
            logger.info(f"✅ 創建 Qdrant 集合 '{QDRANT_COLLECTION}'")

        logger.info(f"✅ Qdrant 初始化成功 (host: {QDRANT_HOST}:{QDRANT_PORT})")
        return True

    except Exception as e:
        logger.error(f"❌ Qdrant 初始化失敗: {e}")
        return False

def generate_content_id(url: str, content: str) -> str:
    """生成內容的唯一 ID"""
    content_hash = hashlib.md5(f"{url}:{content[:100]}".encode()).hexdigest()
    return f"web_{content_hash}"

async def save_to_qdrant(url: str, title: str, content: str, content_type: str = "summary"):
    """將內容保存到 Qdrant"""
    if not qdrant_client or not embedding_model:
        return False

    try:
        # 生成 embedding
        text_to_embed = f"{title} {content}"
        embedding = embedding_model.encode(text_to_embed)

        # 生成唯一 ID
        point_id = generate_content_id(url, content)

        # 創建點
        point = PointStruct(
            id=point_id,
            vector=embedding.tolist(),
            payload={
                "url": url,
                "title": title,
                "content": content,
                "content_type": content_type,
                "timestamp": datetime.now().isoformat(),
                "source": "websearch"
            }
        )

        # 保存到 Qdrant
        qdrant_client.upsert(
            collection_name=QDRANT_COLLECTION,
            points=[point]
        )

        logger.info(f"✅ 保存到 Qdrant: {url} ({content_type})")
        return True

    except Exception as e:
        logger.error(f"❌ 保存到 Qdrant 失敗: {e}")
        return False

#, safe=''
# --- 配置 Trafilatura ---
config = use_config()
config.set("DEFAULT", "EXTRACTION_TIMEOUT", "30")
config.set("DEFAULT", "MIN_EXTRACTED_SIZE", "200")
config.set("DEFAULT", "MIN_OUTPUT_SIZE", "100")

# --- HTTP client with improved settings ---
timeout = httpx.Timeout(connect=10.0, read=30.0, write=10.0, pool=15.0)
limits = httpx.Limits(max_keepalive_connections=20, max_connections=100)
client = httpx.AsyncClient(
    headers=HEADERS,
    timeout=timeout,
    limits=limits,
    transport=httpx.AsyncHTTPTransport(retries=2)
)

# --- 重試機制 ---
async def _retry_request(url: str, max_retries: int = 3, use_proxy: bool = True) -> str:
    """帶重試機制的請求函數，支援 fallback 到直接存取"""
    last_exception = None

    for attempt in range(max_retries):
        try:
            target_url = to_proxy(url) if use_proxy else url

            # 添加隨機延遲避免被封鎖
            if attempt > 0:
                delay = random.uniform(1, 3) * attempt
                await asyncio.sleep(delay)
                logger.info(f"🔄 重試第 {attempt + 1} 次: {url}")

            resp = await client.get(
                target_url,
                timeout=httpx.Timeout(15 if use_proxy else 20, connect=10),
                headers=HEADERS
            )
            resp.raise_for_status()

            status = resp.headers.get("X-Cache-Status", "UNKNOWN")
            logger.info(f"✅ 成功取得 {url} (cache: {status}, len: {len(resp.text)})")
            return resp.text

        except Exception as e:
            last_exception = e
            logger.warning(f"⚠️ 嘗試 {attempt + 1} 失敗: {url} - {str(e)}")

            # 如果是 proxy 失敗且還有重試次數，嘗試直接存取
            if use_proxy and attempt < max_retries - 1:
                logger.info(f"🔄 嘗試直接存取: {url}")
                try:
                    resp = await client.get(
                        url,
                        timeout=httpx.Timeout(20, connect=10),
                        headers=HEADERS
                    )
                    resp.raise_for_status()
                    logger.info(f"✅ 直接存取成功: {url}")
                    return resp.text
                except Exception as direct_e:
                    logger.warning(f"⚠️ 直接存取也失敗: {url} - {str(direct_e)}")

    raise last_exception or Exception("所有重試都失敗")

# --- 使用 Trafilatura 抓取摘要 ---
async def _fetch_summary(url: str, max_len: int = MAX_CHARS_PER_PAGE) -> str:
    try:
        html_content = await _retry_request(url)

        # 使用 Trafilatura 提取內容
        extracted = trafilatura.extract(
            html_content,
            config=config,
            include_comments=False,
            include_tables=True,
            no_fallback=False
        )

        if not extracted:
            logger.warning(f"⚠️ Trafilatura 無法提取內容: {url}")
            return "(無法提取內容)"

        # 清理和截斷文本
        clean_text = " ".join(extracted.split())
        preview = clean_text[:120] if len(clean_text) > 120 else clean_text
        logger.info(f"📄 提取成功 len={len(clean_text)} preview={preview}")

        return clean_text[:max_len]

    except Exception as e:
        logger.error(f"❌ 摘要提取失敗 {url}: {str(e)}")
        return f"(擷取失敗: {e})"

# --- 使用 Trafilatura 抓取全文 ---
async def _fetch_full_text(url: str, max_len: int = MAX_FULL_TEXT_LENGTH) -> str:
    try:
        html_content = await _retry_request(url)

        # 使用 Trafilatura 提取完整內容
        extracted = trafilatura.extract(
            html_content,
            config=config,
            include_comments=False,
            include_tables=True,
            include_links=True,
            no_fallback=False,
            favor_precision=False,  # 偏向召回率，獲取更多內容
            favor_recall=True
        )

        if not extracted:
            logger.warning(f"⚠️ Trafilatura 無法提取全文: {url}")
            # Fallback: 嘗試提取 metadata
            metadata = trafilatura.extract_metadata(html_content)
            if metadata and metadata.title:
                return f"標題: {metadata.title}\n(內容提取失敗)"
            return "(無法提取內容)"

        # 清理文本
        clean_text = " ".join(extracted.split())
        preview = clean_text[:120] if len(clean_text) > 120 else clean_text
        logger.info(f"📄 全文提取成功 len={len(clean_text)} preview={preview}")

        return clean_text[:max_len]

    except Exception as e:
        logger.error(f"❌ 全文提取失敗 {url}: {str(e)}")
        return f"(全文擷取失敗: {e})"


# --- 快速搜尋函數：全網搜尋 ---
@kernel_function(name="search_titles_only", description="快速搜尋全網，只回傳標題和網址")
async def search_titles_only(query: str) -> list:
    """快速搜尋全網，讓模型先看到所有標題再決定要抓取哪些"""
    try:
        params = {"q": query, "format": "json"}
        search_url = f"{SEARXNG_URL}/search"

        logger.info(f"🔍 全網搜尋: {query}")
        resp = await client.get(search_url, params=params, timeout=httpx.Timeout(15))
        resp.raise_for_status()
        results = resp.json().get("results", [])[:MAX_RESULTS]

        if not results:
            logger.warning(f"⚠️ 搜尋無結果: {query}")
            return [{"error": "搜尋無結果"}]

        output = []
        for idx, r in enumerate(results, 1):
            output.append({
                "index": idx,
                "title": r["title"],
                "url": r["url"],
                "summary": "(尚未抓取，請使用 fetch_summaries 或 fetch_full_text)"
            })

        logger.info(f"✅ 搜尋完成，找到 {len(output)} 筆結果")
        return output

    except Exception as e:
        logger.error(f"❌ 搜尋失敗: {str(e)}")
        return [{"error": str(e)}]

# --- 批量抓取摘要 ---
@kernel_function(name="fetch_summaries", description="根據網址清單批量抓取摘要")
async def fetch_summaries(
    urls: Annotated[list[str], "網址清單"]
) -> list[dict]:
    """批量抓取摘要，不限制並發數量"""
    if not isinstance(urls, list) or not all(isinstance(u, str) for u in urls):
        return [{"error": "urls 必須是字串清單 list[str]"}]

    if len(urls) == 0:
        return [{"error": "網址清單不能為空"}]

    logger.info(f"📄 開始批量抓取 {len(urls)} 個網址的摘要...")

    # 不限制並發數量，全速抓取
    summaries = await asyncio.gather(*[
        _fetch_summary(url) for url in urls
    ], return_exceptions=True)

    result = []
    success_count = 0
    qdrant_save_count = 0

    for url, summary in zip(urls, summaries):
        if isinstance(summary, Exception):
            logger.warning(f"❌ {url} 摘要失敗: {summary!r}")
            summary = f"(摘要擷取失敗: {summary})"
        else:
            success_count += 1

            # 嘗試保存到 Qdrant
            if len(summary) > 50:  # 只保存有意義的內容
                try:
                    # 從 URL 提取標題（簡單方法）
                    title = url.split('/')[-1] or url.split('/')[-2] or "網頁內容"

                    # 異步保存到 Qdrant
                    saved = await save_to_qdrant(url, title, summary, "summary")
                    if saved:
                        qdrant_save_count += 1
                except Exception as e:
                    logger.warning(f"⚠️ 保存到 Qdrant 失敗 {url}: {e}")

        result.append({
            "url": url,
            "summary": summary
        })

    logger.info(f"✅ 摘要抓取完成，成功 {success_count}/{len(urls)} 個網址，保存到 Qdrant: {qdrant_save_count} 筆")
    return result



# --- 爬取全文 MCP function ---
@kernel_function(name="fetch_full_text", description="根據網址清單爬取全文")
async def fetch_full_text(
    urls: Annotated[list[str], "網址清單"]
) -> list[dict]:
    if not isinstance(urls, list) or not all(isinstance(u, str) for u in urls):
        return [{"error": "urls 必須是字串清單 list[str]"}]

    if len(urls) == 0:
        return [{"error": "網址清單不能為空"}]

    logger.info(f"📄 開始抓取 {len(urls)} 個網址的全文...")

    # 全速並發抓取全文，不限制數量
    full_texts = await asyncio.gather(*[
        _fetch_full_text(url) for url in urls
    ], return_exceptions=True)

    result = []
    success_count = 0
    qdrant_save_count = 0

    for url, text in zip(urls, full_texts):
        if isinstance(text, Exception):
            logger.warning(f"❌ {url} 失敗: {text!r}")
            text = f"(爬取失敗: {text})"
        else:
            success_count += 1

            # 嘗試保存到 Qdrant
            if len(text) > 100:  # 只保存有意義的內容
                try:
                    # 從 URL 提取標題（簡單方法）
                    title = url.split('/')[-1] or url.split('/')[-2] or "網頁內容"

                    # 異步保存到 Qdrant
                    saved = await save_to_qdrant(url, title, text[:MAX_FULL_TEXT_LENGTH], "full_text")
                    if saved:
                        qdrant_save_count += 1
                except Exception as e:
                    logger.warning(f"⚠️ 保存到 Qdrant 失敗 {url}: {e}")

        result.append({
            "url": url,
            "full_text": text[:MAX_FULL_TEXT_LENGTH]
        })

    logger.info(f"✅ 全文抓取完成，成功 {success_count}/{len(urls)} 個網址，保存到 Qdrant: {qdrant_save_count} 筆")
    return result

# --- 從 Qdrant 搜尋快取內容 ---
@kernel_function(name="search_cached_content", description="從 Qdrant 搜尋已快取的網頁內容")
async def search_cached_content(query: str, limit: int = 5) -> list:
    """從 Qdrant 搜尋已快取的內容"""
    if not qdrant_client or not embedding_model:
        return [{"error": "Qdrant 未初始化"}]

    try:
        # 生成查詢向量
        query_vector = embedding_model.encode(query)

        # 搜尋 Qdrant
        search_results = qdrant_client.search(
            collection_name=QDRANT_COLLECTION,
            query_vector=query_vector.tolist(),
            limit=limit,
            score_threshold=0.5  # 最低相似度閾值
        )

        if not search_results:
            logger.info(f"🔍 Qdrant 搜尋無結果: {query}")
            return [{"message": "快取中沒有相關內容"}]

        results = []
        for result in search_results:
            payload = result.payload
            results.append({
                "url": payload["url"],
                "title": payload["title"],
                "content": payload["content"],
                "content_type": payload["content_type"],
                "score": result.score,
                "timestamp": payload["timestamp"]
            })

        logger.info(f"✅ Qdrant 搜尋完成，找到 {len(results)} 筆快取結果")
        return results

    except Exception as e:
        logger.error(f"❌ Qdrant 搜尋失敗: {e}")
        return [{"error": str(e)}]

# --- 啟動 MCP Server ---
def run() -> None:
    # 初始化 Qdrant
    qdrant_status = init_qdrant()
    if qdrant_status:
        logger.info("🎯 Qdrant 整合已啟用，搜尋結果將自動保存")
    else:
        logger.warning("⚠️ Qdrant 整合未啟用，僅提供搜尋功能")

    kernel = Kernel()
    kernel.add_function("websearch", search_titles_only)     # 快速搜尋
    kernel.add_function("fetcher", fetch_summaries)          # 批量摘要
    kernel.add_function("fetcher", fetch_full_text)          # 全文抓取
    kernel.add_function("qdrant", search_cached_content)     # 搜尋快取內容

    server = kernel.as_mcp_server(server_name="websearch")

    async def handle_stdin():
        print("✅ MCP Server 正在啟動...")
        async with stdio_server() as (read_stream, write_stream):
            try:
                await server.run(read_stream, write_stream, server.create_initialization_options())
            finally:
                await client.aclose()

    asyncio.run(handle_stdin())

if __name__ == "__main__":
    run()























#還留著nkust網域
# import os
# import asyncio
# import html
# import httpx
# import trafilatura
# from trafilatura.settings import use_config
# from dotenv import load_dotenv
# from mcp.server.stdio import stdio_server
# from semantic_kernel import Kernel
# from semantic_kernel.functions import kernel_function
# from typing import Annotated
# from loguru import logger
# from logger_utils import configure_logging, log_timing
# configure_logging("INFO")
# from urllib.parse import quote, unquote
# import time
# import random

# GATEWAY = "https://b225.54ucl.com"

# # --- 環境變數載入 ---
# load_dotenv()

# # --- 常數與初始化 ---
# HEADERS = {
#     "User-Agent": (
#         "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
#         "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
#     )
# }
# SEARXNG_URL = os.environ.get("SEARXNG_URL", "http://localhost:4000").rstrip("/")
# MAX_CHARS_PER_PAGE = 300  # 摘要最大長度
# MAX_FULL_TEXT_LENGTH = 5000  # 全文最大長度
# MAX_RESULTS = 10  # 搜尋最多10筆

# def to_proxy(url: str) -> str:
#     return f"{GATEWAY}/ui/proxy/?target={url}"

# #, safe=''
# # --- 配置 Trafilatura ---
# config = use_config()
# config.set("DEFAULT", "EXTRACTION_TIMEOUT", "30")
# config.set("DEFAULT", "MIN_EXTRACTED_SIZE", "200")
# config.set("DEFAULT", "MIN_OUTPUT_SIZE", "100")

# # --- HTTP client with improved settings ---
# timeout = httpx.Timeout(connect=10.0, read=30.0, write=10.0, pool=15.0)
# limits = httpx.Limits(max_keepalive_connections=20, max_connections=100)
# client = httpx.AsyncClient(
#     headers=HEADERS,
#     timeout=timeout,
#     limits=limits,
#     transport=httpx.AsyncHTTPTransport(retries=2)
# )

# # --- 重試機制 ---
# async def _retry_request(url: str, max_retries: int = 3, use_proxy: bool = True) -> str:
#     """帶重試機制的請求函數，支援 fallback 到直接存取"""
#     last_exception = None

#     for attempt in range(max_retries):
#         try:
#             target_url = to_proxy(url) if use_proxy else url

#             # 添加隨機延遲避免被封鎖
#             if attempt > 0:
#                 delay = random.uniform(1, 3) * attempt
#                 await asyncio.sleep(delay)
#                 logger.info(f"🔄 重試第 {attempt + 1} 次: {url}")

#             resp = await client.get(
#                 target_url,
#                 timeout=httpx.Timeout(15 if use_proxy else 20, connect=10),
#                 headers=HEADERS
#             )
#             resp.raise_for_status()

#             status = resp.headers.get("X-Cache-Status", "UNKNOWN")
#             logger.info(f"✅ 成功取得 {url} (cache: {status}, len: {len(resp.text)})")
#             return resp.text

#         except Exception as e:
#             last_exception = e
#             logger.warning(f"⚠️ 嘗試 {attempt + 1} 失敗: {url} - {str(e)}")

#             # 如果是 proxy 失敗且還有重試次數，嘗試直接存取
#             if use_proxy and attempt < max_retries - 1:
#                 logger.info(f"🔄 嘗試直接存取: {url}")
#                 try:
#                     resp = await client.get(
#                         url,
#                         timeout=httpx.Timeout(20, connect=10),
#                         headers=HEADERS
#                     )
#                     resp.raise_for_status()
#                     logger.info(f"✅ 直接存取成功: {url}")
#                     return resp.text
#                 except Exception as direct_e:
#                     logger.warning(f"⚠️ 直接存取也失敗: {url} - {str(direct_e)}")

#     raise last_exception or Exception("所有重試都失敗")

# # --- 使用 Trafilatura 抓取摘要 ---
# async def _fetch_summary(url: str, max_len: int = MAX_CHARS_PER_PAGE) -> str:
#     try:
#         html_content = await _retry_request(url)

#         # 使用 Trafilatura 提取內容
#         extracted = trafilatura.extract(
#             html_content,
#             config=config,
#             include_comments=False,
#             include_tables=True,
#             no_fallback=False
#         )

#         if not extracted:
#             logger.warning(f"⚠️ Trafilatura 無法提取內容: {url}")
#             return "(無法提取內容)"

#         # 清理和截斷文本
#         clean_text = " ".join(extracted.split())
#         preview = clean_text[:120] if len(clean_text) > 120 else clean_text
#         logger.info(f"📄 提取成功 len={len(clean_text)} preview={preview}")

#         return clean_text[:max_len]

#     except Exception as e:
#         logger.error(f"❌ 摘要提取失敗 {url}: {str(e)}")
#         return f"(擷取失敗: {e})"

# # --- 使用 Trafilatura 抓取全文 ---
# async def _fetch_full_text(url: str, max_len: int = MAX_FULL_TEXT_LENGTH) -> str:
#     try:
#         html_content = await _retry_request(url)

#         # 使用 Trafilatura 提取完整內容
#         extracted = trafilatura.extract(
#             html_content,
#             config=config,
#             include_comments=False,
#             include_tables=True,
#             include_links=True,
#             no_fallback=False,
#             favor_precision=False,  # 偏向召回率，獲取更多內容
#             favor_recall=True
#         )

#         if not extracted:
#             logger.warning(f"⚠️ Trafilatura 無法提取全文: {url}")
#             # Fallback: 嘗試提取 metadata
#             metadata = trafilatura.extract_metadata(html_content)
#             if metadata and metadata.title:
#                 return f"標題: {metadata.title}\n(內容提取失敗)"
#             return "(無法提取內容)"

#         # 清理文本
#         clean_text = " ".join(extracted.split())
#         preview = clean_text[:120] if len(clean_text) > 120 else clean_text
#         logger.info(f"📄 全文提取成功 len={len(clean_text)} preview={preview}")

#         return clean_text[:max_len]

#     except Exception as e:
#         logger.error(f"❌ 全文提取失敗 {url}: {str(e)}")
#         return f"(全文擷取失敗: {e})"


# # --- 快速搜尋函數：優先 NKUST 網域 ---
# @kernel_function(name="search_titles_only", description="快速搜尋，優先搜尋 nkust.edu.tw 網域，只回傳標題和網址")
# async def search_titles_only(query: str) -> list:
#     """快速搜尋，優先搜尋高科大網域，讓模型先看到所有標題再決定要抓取哪些"""
#     try:
#         # 優先搜尋 nkust.edu.tw 網域
#         nkust_query = f"site:nkust.edu.tw {query}"
#         params = {"q": nkust_query, "format": "json"}
#         search_url = f"{SEARXNG_URL}/search"

#         logger.info(f"🔍 優先搜尋高科大網域: {nkust_query}")
#         resp = await client.get(search_url, params=params, timeout=httpx.Timeout(15))
#         resp.raise_for_status()
#         results = resp.json().get("results", [])[:MAX_RESULTS]

#         # 如果高科大網域沒有足夠結果，補充全網搜尋
#         if len(results) < 5:
#             logger.info(f"🔍 高科大網域只找到 {len(results)} 筆，補充全網搜尋...")
#             general_params = {"q": query, "format": "json"}
#             general_resp = await client.get(search_url, params=general_params, timeout=httpx.Timeout(15))
#             general_resp.raise_for_status()
#             general_results = general_resp.json().get("results", [])

#             # 合併結果，去重複
#             existing_urls = {r["url"] for r in results}
#             for r in general_results:
#                 if r["url"] not in existing_urls and len(results) < MAX_RESULTS:
#                     results.append(r)
#                     existing_urls.add(r["url"])

#         if not results:
#             logger.warning(f"⚠️ 搜尋無結果: {query}")
#             return [{"error": "搜尋無結果"}]

#         output = []
#         nkust_count = 0
#         for idx, r in enumerate(results, 1):
#             is_nkust = "nkust.edu.tw" in r["url"]
#             if is_nkust:
#                 nkust_count += 1

#             output.append({
#                 "index": idx,
#                 "title": r["title"],
#                 "url": r["url"],
#                 "domain": "nkust.edu.tw" if is_nkust else "其他網域",
#                 "summary": "(尚未抓取，請使用 fetch_summaries 或 fetch_full_text)"
#             })

#         logger.info(f"✅ 快速搜尋完成，找到 {len(output)} 筆結果 (高科大: {nkust_count} 筆)")
#         return output

#     except Exception as e:
#         logger.error(f"❌ 快速搜尋失敗: {str(e)}")
#         return [{"error": str(e)}]

# # --- 批量抓取摘要 ---
# @kernel_function(name="fetch_summaries", description="根據網址清單批量抓取摘要")
# async def fetch_summaries(
#     urls: Annotated[list[str], "網址清單"]
# ) -> list[dict]:
#     """批量抓取摘要，不限制並發數量"""
#     if not isinstance(urls, list) or not all(isinstance(u, str) for u in urls):
#         return [{"error": "urls 必須是字串清單 list[str]"}]

#     if len(urls) == 0:
#         return [{"error": "網址清單不能為空"}]

#     logger.info(f"📄 開始批量抓取 {len(urls)} 個網址的摘要...")

#     # 不限制並發數量，全速抓取
#     summaries = await asyncio.gather(*[
#         _fetch_summary(url) for url in urls
#     ], return_exceptions=True)

#     result = []
#     success_count = 0
#     for url, summary in zip(urls, summaries):
#         if isinstance(summary, Exception):
#             logger.warning(f"❌ {url} 摘要失敗: {summary!r}")
#             summary = f"(摘要擷取失敗: {summary})"
#         else:
#             success_count += 1

#         result.append({
#             "url": url,
#             "summary": summary
#         })

#     logger.info(f"✅ 摘要抓取完成，成功 {success_count}/{len(urls)} 個網址")
#     return result



# # --- 爬取全文 MCP function ---
# @kernel_function(name="fetch_full_text", description="根據網址清單爬取全文")
# async def fetch_full_text(
#     urls: Annotated[list[str], "網址清單"]
# ) -> list[dict]:
#     if not isinstance(urls, list) or not all(isinstance(u, str) for u in urls):
#         return [{"error": "urls 必須是字串清單 list[str]"}]

#     if len(urls) == 0:
#         return [{"error": "網址清單不能為空"}]

#     logger.info(f"📄 開始抓取 {len(urls)} 個網址的全文...")

#     # 全速並發抓取全文，不限制數量
#     full_texts = await asyncio.gather(*[
#         _fetch_full_text(url) for url in urls
#     ], return_exceptions=True)

#     result = []
#     success_count = 0
#     for url, text in zip(urls, full_texts):
#         if isinstance(text, Exception):
#             logger.warning(f"❌ {url} 失敗: {text!r}")
#             text = f"(爬取失敗: {text})"
#         else:
#             success_count += 1

#         result.append({
#             "url": url,
#             "full_text": text[:MAX_FULL_TEXT_LENGTH]
#         })

#     logger.info(f"✅ 全文抓取完成，成功 {success_count}/{len(urls)} 個網址")
#     return result

# # --- 啟動 MCP Server ---
# def run() -> None:
#     kernel = Kernel()
#     kernel.add_function("websearch", search_titles_only)  # 快速搜尋
#     kernel.add_function("fetcher", fetch_summaries)       # 批量摘要
#     kernel.add_function("fetcher", fetch_full_text)       # 全文抓取

#     server = kernel.as_mcp_server(server_name="websearch")

#     async def handle_stdin():
#         print("✅ MCP Server 正在啟動...")
#         async with stdio_server() as (read_stream, write_stream):
#             try:
#                 await server.run(read_stream, write_stream, server.create_initialization_options())
#             finally:
#                 await client.aclose()

#     asyncio.run(handle_stdin())

# if __name__ == "__main__":
#     run()






