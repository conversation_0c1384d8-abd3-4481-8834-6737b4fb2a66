#!/usr/bin/env python3
"""
測試模型回應問題
"""

import asyncio
import aiohttp
import json

async def test_chat_response():
    """測試聊天回應"""
    
    url = "http://localhost:8088/ui/chat"
    
    # 測試不同模型
    test_cases = [
        {
            "name": "o3-mini 預設模式",
            "data": {
                "user_id": "test_user_o3",
                "message": "你好，請簡單介紹一下你自己",
                "model_id": "o3-mini",
                "mode": "default"
            }
        },
        {
            "name": "deepseek 模式",
            "data": {
                "user_id": "test_user_deepseek",
                "message": "你好，請簡單介紹一下你自己",
                "model_id": "deepseek",
                "mode": "deepseek"
            }
        },
        {
            "name": "自定義角色",
            "data": {
                "user_id": "test_user_custom",
                "message": "你好，請簡單介紹一下你自己",
                "model_id": "o3-mini",
                "mode": "custom_57121e52"  # 使用之前創建的角色
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 測試: {test_case['name']}")
        print("=" * 50)
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=test_case['data']) as response:
                    print(f"狀態碼: {response.status}")
                    
                    if response.status == 200:
                        print("回應內容:")
                        print("-" * 30)
                        
                        content_length = 0
                        async for chunk in response.content.iter_chunked(1024):
                            if chunk:
                                text = chunk.decode('utf-8')
                                content_length += len(text)
                                print(text, end='')
                        
                        print(f"\n-" * 30)
                        print(f"總長度: {content_length} 字元")
                        
                        if content_length == 0:
                            print("⚠️ 警告: 回應為空!")
                        else:
                            print("✅ 回應正常")
                    else:
                        error_text = await response.text()
                        print(f"❌ 錯誤: {error_text}")
                        
        except Exception as e:
            print(f"❌ 連接錯誤: {e}")

async def test_server_status():
    """測試服務器狀態"""
    print("🔍 檢查服務器狀態")
    print("=" * 50)
    
    try:
        async with aiohttp.ClientSession() as session:
            # 測試靜態頁面
            async with session.get("http://localhost:8088/ui/") as response:
                print(f"靜態頁面狀態: {response.status}")
            
            # 測試角色列表 API
            async with session.get("http://localhost:8088/ui/roles") as response:
                print(f"角色列表 API 狀態: {response.status}")
                if response.status == 200:
                    roles = await response.json()
                    print(f"可用角色數量: {len(roles)}")
                    
                    # 列出前幾個角色
                    for i, (role_id, config) in enumerate(list(roles.items())[:3]):
                        display_name = config.get('display_name', f"{config.get('organization_name', '')} - {config.get('role_title', '')}")
                        print(f"  {i+1}. {role_id}: {display_name}")
                        
    except Exception as e:
        print(f"❌ 服務器連接失敗: {e}")
        print("💡 請確認服務器是否正在運行: python web_app.py")

async def main():
    """主測試函數"""
    print("🚀 開始測試模型回應")
    print("=" * 60)
    
    # 先檢查服務器狀態
    await test_server_status()
    
    # 然後測試聊天回應
    await test_chat_response()
    
    print("\n🎯 測試完成!")

if __name__ == "__main__":
    asyncio.run(main())
