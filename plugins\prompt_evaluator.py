"""
Prompt 評分 Agent - 負責評估和改進生成的 system prompt
"""

import json
from typing import Dict, List, Any, Tuple
from loguru import logger

class PromptEvaluator:
    """負責評估 system prompt 品質的 AI Agent"""
    
    def __init__(self, kernel=None):
        self.kernel = kernel
        
    def evaluate_system_prompt(self, system_prompt: str, user_requirements: Dict[str, str]) -> <PERSON><PERSON>[float, List[str]]:
        """評估 system prompt 品質並提供改進建議

        Returns:
            Tuple[float, List[str]]: (分數 0-10, 改進建議列表)
        """
        score = 0.0
        suggestions = []

        # 評估項目和權重
        evaluations = [
            (self._evaluate_prompt_completeness, 2.0, "完整性"),
            (self._evaluate_prompt_role_consistency, 2.0, "角色一致性"),
            (self._evaluate_prompt_structure, 2.0, "結構品質"),
            (self._evaluate_prompt_customization, 2.0, "客製化程度"),
            (self._evaluate_prompt_clarity, 1.5, "清晰度"),
            (self._evaluate_prompt_practicality, 0.5, "實用性")
        ]

        total_weight = sum(weight for _, weight, _ in evaluations)

        for eval_func, weight, category in evaluations:
            try:
                category_score, category_suggestions = eval_func(system_prompt, user_requirements)
                weighted_score = (category_score / 10.0) * weight
                score += weighted_score

                if category_suggestions:
                    suggestions.extend([f"[{category}] {s}" for s in category_suggestions])

                logger.debug(f"{category}: {category_score}/10 (權重: {weight})")

            except Exception as e:
                logger.error(f"評估 {category} 時發生錯誤: {e}")
                suggestions.append(f"[{category}] 評估過程中發生錯誤")

        # 正規化分數到 0-10
        final_score = (score / total_weight) * 10

        return round(final_score, 1), suggestions

    def evaluate_config(self, config: Dict[str, Any], user_requirements: Dict[str, str]) -> Tuple[float, List[str]]:
        """評估配置品質並提供改進建議（保持向後兼容）

        Returns:
            Tuple[float, List[str]]: (分數 0-10, 改進建議列表)
        """
        # 如果配置中有 system_prompt，則評估它
        if "system_prompt" in config:
            return self.evaluate_system_prompt(config["system_prompt"], user_requirements)

        # 否則使用原有的配置評估邏輯
        score = 0.0
        suggestions = []
        
        # 評估項目和權重
        evaluations = [
            (self._evaluate_completeness, 2.0, "完整性"),
            (self._evaluate_role_consistency, 2.0, "角色一致性"),
            (self._evaluate_special_rules_quality, 2.0, "規則品質"),
            (self._evaluate_tool_configuration, 1.5, "工具配置"),
            (self._evaluate_language_tone, 1.5, "語言語調"),
            (self._evaluate_customization, 1.0, "客製化程度")
        ]
        
        total_weight = sum(weight for _, weight, _ in evaluations)
        
        for eval_func, weight, category in evaluations:
            try:
                category_score, category_suggestions = eval_func(config, user_requirements)
                weighted_score = (category_score / 10.0) * weight
                score += weighted_score
                
                if category_suggestions:
                    suggestions.extend([f"[{category}] {s}" for s in category_suggestions])
                    
                logger.debug(f"{category}: {category_score}/10 (權重: {weight})")
                
            except Exception as e:
                logger.error(f"評估 {category} 時發生錯誤: {e}")
                suggestions.append(f"[{category}] 評估過程中發生錯誤")
        
        # 正規化分數到 0-10
        final_score = (score / total_weight) * 10
        
        return round(final_score, 1), suggestions

    def _evaluate_prompt_completeness(self, prompt: str, user_req: Dict[str, str]) -> Tuple[float, List[str]]:
        """評估 system prompt 完整性"""
        suggestions = []
        score = 10.0

        # 檢查必要元素
        required_elements = [
            ("角色身份", ["你是", "助理", "專員"]),
            ("組織名稱", [user_req.get('organization_name', '')]),
            ("工作流程", ["步驟", "流程", "依序"]),
            ("工具使用", ["time-GetDate", "search_titles_only", "fetch_summaries"]),
            ("規則說明", ["規則", "遵守", "務必"]),
            ("格式要求", ["格式", "換行", "分段"])
        ]

        for element_name, keywords in required_elements:
            if not any(keyword in prompt for keyword in keywords if keyword):
                score -= 1.5
                suggestions.append(f"缺少{element_name}相關內容")

        # 檢查長度
        if len(prompt) < 500:
            score -= 2.0
            suggestions.append("prompt 過短，建議增加更詳細的說明")
        elif len(prompt) > 3000:
            score -= 1.0
            suggestions.append("prompt 過長，建議精簡內容")

        return max(0, score), suggestions

    def _evaluate_prompt_role_consistency(self, prompt: str, user_req: Dict[str, str]) -> Tuple[float, List[str]]:
        """評估角色一致性"""
        suggestions = []
        score = 10.0

        role_name = user_req.get('role_name', '')
        org_name = user_req.get('organization_name', '')

        # 檢查角色是否在 prompt 中被明確提及
        if role_name and role_name not in prompt:
            score -= 3.0
            suggestions.append(f"未明確提及角色「{role_name}」")

        if org_name and org_name not in prompt:
            score -= 3.0
            suggestions.append(f"未明確提及組織「{org_name}」")

        # 檢查語調一致性
        tone_style = user_req.get('tone_style', '')
        if tone_style and tone_style not in prompt:
            score -= 1.5
            suggestions.append(f"未體現指定的語調風格「{tone_style}」")

        return max(0, score), suggestions

    def _evaluate_prompt_structure(self, prompt: str, user_req: Dict[str, str]) -> Tuple[float, List[str]]:
        """評估 prompt 結構品質"""
        suggestions = []
        score = 10.0

        # 檢查是否有清晰的步驟結構
        step_patterns = ["1.", "2.", "3.", "4.", "5."]
        found_steps = sum(1 for pattern in step_patterns if pattern in prompt)

        if found_steps < 3:
            score -= 2.0
            suggestions.append("缺少清晰的步驟結構，建議使用編號列表")

        # 檢查是否有適當的分段
        paragraphs = prompt.split('\n\n')
        if len(paragraphs) < 3:
            score -= 1.5
            suggestions.append("段落分隔不足，建議增加空行分段")

        # 檢查是否有格式化元素
        formatting_elements = ["**", "- ", "1. ", "2. "]
        if not any(element in prompt for element in formatting_elements):
            score -= 1.0
            suggestions.append("缺少格式化元素，建議使用粗體和列表")

        return max(0, score), suggestions

    def _evaluate_prompt_customization(self, prompt: str, user_req: Dict[str, str]) -> Tuple[float, List[str]]:
        """評估客製化程度"""
        suggestions = []
        score = 10.0

        # 檢查特殊要求是否被納入
        special_req = user_req.get('special_requirements', '')
        if special_req and len(special_req) > 10:
            # 簡單檢查是否有相關關鍵詞
            req_keywords = special_req.split()[:3]  # 取前3個詞
            if not any(keyword in prompt for keyword in req_keywords):
                score -= 2.0
                suggestions.append("未充分體現用戶的特殊要求")

        # 檢查組織網域是否被使用
        domain = user_req.get('organization_domain', '')
        if domain and domain not in prompt:
            score -= 1.0
            suggestions.append(f"未使用組織網域「{domain}」")

        # 檢查是否有通用模板痕跡
        generic_phrases = ["組織", "公司", "機構", "[", "]"]
        generic_count = sum(1 for phrase in generic_phrases if phrase in prompt)
        if generic_count > 2:
            score -= 1.5
            suggestions.append("包含過多通用模板內容，建議更具體化")

        return max(0, score), suggestions

    def _evaluate_prompt_clarity(self, prompt: str, user_req: Dict[str, str]) -> Tuple[float, List[str]]:
        """評估清晰度"""
        suggestions = []
        score = 10.0

        # 檢查句子長度
        sentences = prompt.split('。')
        long_sentences = [s for s in sentences if len(s) > 100]
        if len(long_sentences) > 3:
            score -= 1.5
            suggestions.append("部分句子過長，建議分割為較短的句子")

        # 檢查重複內容
        lines = prompt.split('\n')
        unique_lines = set(line.strip() for line in lines if line.strip())
        if len(lines) - len(unique_lines) > 2:
            score -= 1.0
            suggestions.append("存在重複內容，建議去除冗餘")

        return max(0, score), suggestions

    def _evaluate_prompt_practicality(self, prompt: str, user_req: Dict[str, str]) -> Tuple[float, List[str]]:
        """評估實用性"""
        suggestions = []
        score = 10.0

        # 檢查是否包含實際可執行的指令
        actionable_keywords = ["使用", "調用", "執行", "搜尋", "分析", "選擇"]
        actionable_count = sum(1 for keyword in actionable_keywords if keyword in prompt)

        if actionable_count < 5:
            score -= 2.0
            suggestions.append("缺少具體可執行的指令，建議增加更多操作性描述")

        return max(0, score), suggestions

    def _evaluate_completeness(self, config: Dict[str, Any], user_req: Dict[str, str]) -> Tuple[float, List[str]]:
        """評估配置完整性"""
        required_fields = [
            'organization_name', 'role_title', 'language', 'tone',
            'content_policy', 'tools', 'special_rules'
        ]
        
        missing_fields = [field for field in required_fields if not config.get(field)]
        
        if missing_fields:
            return 3.0, [f"缺少必要欄位: {', '.join(missing_fields)}"]
        
        # 檢查 special_rules 數量
        rules = config.get('special_rules', [])
        if len(rules) < 5:
            return 6.0, ["special_rules 數量不足，建議至少 5-8 條規則"]
        elif len(rules) > 12:
            return 7.0, ["special_rules 過多，建議精簡到 8-10 條核心規則"]
        
        return 9.0, []
    
    def _evaluate_role_consistency(self, config: Dict[str, Any], user_req: Dict[str, str]) -> Tuple[float, List[str]]:
        """評估角色一致性"""
        suggestions = []
        score = 10.0
        
        role_title = config.get('role_title', '')
        org_name = config.get('organization_name', '')
        tone = config.get('tone', '')
        rules = config.get('special_rules', [])
        
        # 檢查角色是否在規則中被明確提及
        role_mentioned = any(role_title in rule or org_name in rule for rule in rules)
        if not role_mentioned:
            score -= 2.0
            suggestions.append("建議在 special_rules 中明確提及角色身份")
        
        # 檢查語調與角色的匹配度
        professional_roles = ['助理', '顧問', '專家', '教授', '醫師']
        casual_tones = ['輕鬆', '幽默', '隨意']
        
        if any(role in role_title for role in professional_roles) and any(tone_word in tone for tone_word in casual_tones):
            score -= 1.5
            suggestions.append("專業角色建議使用更正式的語調")
        
        return score, suggestions
    
    def _evaluate_special_rules_quality(self, config: Dict[str, Any], user_req: Dict[str, str]) -> Tuple[float, List[str]]:
        """評估特殊規則品質"""
        rules = config.get('special_rules', [])
        suggestions = []
        score = 10.0
        
        if not rules:
            return 0.0, ["缺少 special_rules"]
        
        # 檢查規則的具體性
        vague_rules = [rule for rule in rules if len(rule) < 10 or '...' in rule]
        if vague_rules:
            score -= 2.0
            suggestions.append("部分規則過於模糊，建議更具體化")
        
        # 檢查是否包含基本的 AI 行為準則
        essential_patterns = [
            ('準確', '真實', '正確'),  # 準確性
            ('不可', '避免', '禁止'),  # 限制性
            ('工具', '搜尋', '查詢'),  # 工具使用
            ('角色', '身份', '職責')   # 角色意識
        ]
        
        for pattern_group in essential_patterns:
            if not any(any(pattern in rule for pattern in pattern_group) for rule in rules):
                score -= 1.0
                suggestions.append(f"建議添加關於 {pattern_group[0]} 的規則")
        
        return score, suggestions
    
    def _evaluate_tool_configuration(self, config: Dict[str, Any], user_req: Dict[str, str]) -> Tuple[float, List[str]]:
        """評估工具配置合理性"""
        tools = config.get('tools', {})
        suggestions = []
        score = 10.0
        
        # 基本工具檢查
        if not tools.get('time_required'):
            score -= 1.0
            suggestions.append("建議啟用時間工具，大多數角色都需要時間資訊")
        
        if not tools.get('search_enabled'):
            score -= 1.5
            suggestions.append("建議啟用搜尋功能以獲取最新資訊")
        
        # 根據角色類型調整建議
        role_title = config.get('role_title', '').lower()
        if '客服' in role_title or '助理' in role_title:
            if not tools.get('faq_enabled'):
                score -= 1.0
                suggestions.append("客服/助理角色建議啟用 FAQ 功能")
        
        return score, suggestions
    
    def _evaluate_language_tone(self, config: Dict[str, Any], user_req: Dict[str, str]) -> Tuple[float, List[str]]:
        """評估語言和語調設定"""
        language = config.get('language', '')
        tone = config.get('tone', '')
        suggestions = []
        score = 10.0
        
        if language != '繁體中文':
            score -= 1.0
            suggestions.append("建議使用繁體中文以符合目標用戶")
        
        if not tone or len(tone) < 3:
            score -= 2.0
            suggestions.append("語調描述過於簡單，建議更詳細")
        
        return score, suggestions
    
    def _evaluate_customization(self, config: Dict[str, Any], user_req: Dict[str, str]) -> Tuple[float, List[str]]:
        """評估客製化程度"""
        suggestions = []
        score = 10.0
        
        # 檢查是否有根據用戶需求客製化
        user_role = user_req.get('role_name', '')
        user_org = user_req.get('organization_name', '')
        user_domain = user_req.get('organization_domain', '')
        
        config_role = config.get('role_title', '')
        config_org = config.get('organization_name', '')
        config_domain = config.get('organization_domain', '')
        
        if user_role and user_role.lower() not in config_role.lower():
            score -= 2.0
            suggestions.append("角色名稱與用戶需求不符")
        
        if user_org and user_org.lower() not in config_org.lower():
            score -= 2.0
            suggestions.append("組織名稱與用戶需求不符")
        
        if user_domain and user_domain not in config_domain:
            score -= 1.0
            suggestions.append("組織網域與用戶需求不符")
        
        return score, suggestions
    
    def get_improvement_prompt(self, config: Dict[str, Any], suggestions: List[str], user_req: Dict[str, str]) -> str:
        """生成改進提示詞"""
        return f"""請改進以下組織配置，根據評估建議進行優化：

**當前配置：**
```json
{json.dumps(config, ensure_ascii=False, indent=2)}
```

**改進建議：**
{chr(10).join(f"- {suggestion}" for suggestion in suggestions)}

**用戶原始需求：**
- 角色名稱: {user_req.get('role_name', '未指定')}
- 組織名稱: {user_req.get('organization_name', '未指定')}
- 組織領域: {user_req.get('organization_domain', '未指定')}
- 特殊要求: {user_req.get('special_requirements', '無')}

**改進要求：**
1. 保持 JSON 格式完整
2. 根據建議具體改進每個問題
3. 確保所有欄位都符合用戶需求
4. special_rules 要具體且實用
5. 保持角色一致性

請只回傳改進後的 JSON 配置，不要包含其他說明。"""
