#!/usr/bin/env python3
"""
測試 Rasa 地址查詢功能
"""

import asyncio
import aiohttp
import json

async def test_rasa_address_queries():
    """測試不同的地址查詢問題"""
    
    test_cases = [
        {
            "name": "通用地址查詢",
            "message": "高科的地址",
            "expected": "應該詢問校區"
        },
        {
            "name": "高科大地址",
            "message": "高科大的地址",
            "expected": "應該詢問校區"
        },
        {
            "name": "學校地址",
            "message": "學校地址",
            "expected": "應該詢問校區"
        },
        {
            "name": "具體校區",
            "message": "楠梓校區的地址",
            "expected": "應該直接回答地址"
        },
        {
            "name": "建工校區",
            "message": "建工校區在哪",
            "expected": "應該直接回答地址"
        }
    ]
    
    print("🧪 測試 Rasa 地址查詢功能")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 測試 {i}: {test_case['name']}")
        print("-" * 40)
        print(f"📤 問題: {test_case['message']}")
        print(f"🎯 期望: {test_case['expected']}")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post("https://b225.54ucl.com/ui/rasa", json={
                    "message": test_case['message'],
                    "user_id": f"test_address_{i}",
                    "model_id": "rasa"
                }) as response:
                    
                    print(f"📊 狀態碼: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        print("📥 Rasa 回應:")
                        
                        if isinstance(data, list) and len(data) > 0:
                            for j, item in enumerate(data):
                                text = item.get('text', '')
                                print(f"  {j+1}. {text}")
                                
                                # 分析回應類型
                                if '你想查詢哪個校區' in text or '校區名稱' in text:
                                    print("  ✅ 正確：詢問校區")
                                elif '地址是' in text and ('校區' in text or '高雄市' in text):
                                    print("  ✅ 正確：直接回答地址")
                                elif '抱歉' in text or '不理解' in text:
                                    print("  ❌ 問題：無法理解")
                                else:
                                    print("  ⚠️ 其他回應")
                        else:
                            print("  ❌ 沒有有效回應")
                    
                    else:
                        error_text = await response.text()
                        print(f"❌ Rasa 服務錯誤: {error_text}")
                        
        except Exception as e:
            print(f"❌ 連接錯誤: {e}")
        
        print("\n" + "=" * 60)

async def test_slot_conversation():
    """測試多輪對話中的 slot 填充"""
    print("\n🔄 測試多輪對話 slot 填充")
    print("=" * 60)
    
    conversation = [
        {
            "message": "高科大的地址",
            "expected_response": "詢問校區"
        },
        {
            "message": "楠梓校區",
            "expected_response": "楠梓校區地址"
        }
    ]
    
    user_id = f"test_conversation_{int(asyncio.get_event_loop().time())}"
    
    for i, turn in enumerate(conversation, 1):
        print(f"\n💬 對話輪次 {i}")
        print(f"📤 用戶: {turn['message']}")
        print(f"🎯 期望: {turn['expected_response']}")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post("https://b225.54ucl.com/ui/rasa", json={
                    "message": turn['message'],
                    "user_id": user_id,  # 使用相同的 user_id 保持對話狀態
                    "model_id": "rasa"
                }) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        print("📥 Rasa 回應:")
                        
                        if isinstance(data, list) and len(data) > 0:
                            for item in data:
                                text = item.get('text', '')
                                print(f"  🤖 {text}")
                        
                        # 等待一下，讓 Rasa 處理狀態
                        await asyncio.sleep(1)
                    
                    else:
                        print(f"❌ 錯誤: {response.status}")
                        
        except Exception as e:
            print(f"❌ 錯誤: {e}")

async def main():
    """主測試函數"""
    print("🚀 Rasa 地址查詢測試")
    print("=" * 80)
    
    # 1. 測試各種地址查詢
    await test_rasa_address_queries()
    
    # 2. 測試多輪對話
    await test_slot_conversation()
    
    print("\n🎉 測試完成！")
    print("\n💡 如果 Rasa 無法正確處理「高科的地址」：")
    print("1. 需要重新訓練 Rasa 模型")
    print("2. 檢查 NLU 訓練資料是否足夠")
    print("3. 確認 rules 和 stories 配置正確")

if __name__ == "__main__":
    asyncio.run(main())
