version: "3.1"

rules:

- rule: Say goodbye anytime the user says goodbye
  steps:
  - intent: goodbye
  - action: utter_goodbye

- rule: Say 'I am a bot' anytime the user challenges
  steps:
  - intent: bot_challenge
  - action: utter_iamabot

- rule: 回答校區地址
  steps:
    - intent: ask_office_location
    - slot_was_set:
        - campus: 楠梓校區
    - action: utter_reply_campus

- rule: 詢問使用者校區
  condition:
    - slot_was_set:
        - campus: null
  steps:
    - intent: ask_office_location
    - action: utter_ask_campus

- rule: 觸發 fallback 條件時執行 fallback action
  steps:
    - intent: nlu_fallback
    - action: action_fallback_handle