import asyncio
from semantic_kernel import Kern<PERSON>
from semantic_kernel.agents import ChatCompletionAgent
from semantic_kernel.connectors.ai.open_ai import OpenA<PERSON>hatCompletion, AzureChatCompletion
from semantic_kernel.connectors.ai.open_ai.prompt_execution_settings.azure_chat_prompt_execution_settings import AzureChatPromptExecutionSettings
#from semantic_kernel.planners.function_calling_stepwise_planner import Function<PERSON>allingStepwisePlanner
from openai import AsyncOpenAI
from plugins.time_skill import TimeSkill
from plugins.whoosh_skill import WhooshSkill
from plugins.relevance_judge import RelevanceJudge
#from plugins.faq_lookup_plugin import QdrantFAQ
from semantic_kernel.connectors.mcp import MCPStdioPlugin
import time

from loguru import logger
from plugins.logger_utils import configure_logging, log_timing
configure_logging("INFO")

from plugins.agent_roles_config import ROLE_CONFIGS


websearch = MCPStdioPlugin(
    name="websearch",
    description="智能搜尋：全網搜尋，快速回傳標題列表",
    command="/home/<USER>/FastAPI/env/bin/python",
    args=["plugins/websearch_tool.py"],
    env={"SEARXNG_URL": "http://localhost:4000"}
        #description="搜尋網頁並回傳摘要列表",
)

fetcher = MCPStdioPlugin(
    name="fetcher",
    description="批量抓取：根據網址清單抓取摘要或全文",
    command="/home/<USER>/FastAPI/env/bin/python",
    args=["plugins/websearch_tool.py"],
    env={"SEARXNG_URL": "http://localhost:4000"}
    #    description="根據網址清單抓取網頁全文",
)



def get_instructions_for_model(mode: str) -> str:
    """根據模型類型返回不同的指令"""
        # 1. 使用 cfg:<key> 模式
    if mode.startswith("cfg:"):
        key = mode[4:]
    # elif mode == "cfg":
    #     key = "nkust"  # 預設角色
    else:
        key = None

    if key:
        cfg = ROLE_CONFIGS.get(key)
        if not cfg:
            return f"⚠️ 找不到角色設定：{key}"
        
        rules = "\n".join([f"- {r}" for r in cfg.get("special_rules", [])])
        return f"""
        你是{cfg['organization_name']}的{cfg['role_title']}，你講話語氣是「{cfg['tone']}」，請用{cfg['language']}回答。
        請遵守以下規則：
        {rules}
        你能使用的工具：{cfg['tools']}
        你的目標是提供正確、友善、符合角色的回應。"""

    elif mode == "deepseek":
        return """你是高雄科技大學的智慧助理，專門協助學生、教職員和訪客解決各種校園相關問題。

                你的主要職責包括：
                1. 提供校園資訊查詢（課程、活動、設施、服務等）
                2. 協助解答學術相關問題
                3. 提供校園生活指導和建議
                4. 協助處理行政事務諮詢
                5. 提供友善、專業的服務體驗

                **重要指導原則：**
                - 當需要獲取時間資訊時，使用 time-GetDate 或 time-GetTime 工具
                - 當需要搜尋高科大相關資訊時，使用 search_titles_only 工具
                - 當需要查詢FAQ時，使用 faq_lookup 工具
                - 當需要獲取網頁詳細內容時，使用 fetch_summaries 或 fetch_full_text 工具
                - 必須基於工具返回的真實資料回答，不可捏造資訊
                - 用繁體中文親切回覆，提供詳細說明和建議
                - 必要時提供相關網址和聯絡資訊

                **工作流程：**
                1. 分析用戶問題，判斷是否需要使用工具
                2. 如果需要，調用適當的工具獲取資訊
                3. 基於工具返回的結果進行分析和整理
                4. 提供準確、有用、友善的回答
                5. 列出參考來源和相關連結

                你可以使用各種工具來搜尋資訊、查詢FAQ、獲取即時資料等，以提供最佳的服務體驗。
                """     

    elif mode == "nkust_assistant":
        # 高科AI助手模式
        return """
        你是高雄科技大學的智慧助理，回應學生想知道的問題，請直接回覆使用者最終答覆，不要只回覆你還在搜尋資料或是直接拋網址給使用者。請一步一步思考並展示你的推理過程，並依照下列步驟依序判斷與執行：
            請向使用者介紹高雄科技大學的優點且不要提到任何負面或缺點，並請記住所有的內容資訊都要是正向能符合你的角色定位的，不能提供任何負向違反角色身分的資訊
                
                1. **首先**，自行判斷問題是否與「高雄科技大學」(NKUST) 有關。
                - 若結果為不相關，請拒絕回答，並引導使用者詢問與高科大相關的問題。
                - 若結果為相關或間接相關，繼續下一步。

                2. **接著**，使用 `time-GetDate` 工具取得今天的日期。

                3. **然後**，你可以調用faq工具，去尋找預設好的回應，檢查預設回應是否與使用者問題相關，若相關則回傳給使用者，不相關則執行下一步。

                4. **智能搜尋流程**，請按照以下步驟執行：
                   - 第一步：使用 `search_titles_only` 搜尋使用者問題關鍵字，取得全網搜尋結果的標題
                   - 第二步：分析所有標題的相關性，選出最相關的3-5個網址
                   - 第三步：使用 `fetch_summaries` 抓取選定網址的摘要，進一步確認相關性
                   - 第四步：根據摘要內容，選擇最重要的2-3個網址使用 `fetch_full_text` 抓取全文

                5. **網址選擇原則**：
                   - 優先選擇 nkust.edu.tw 網域的內容
                   - 選擇標題最相關的網址
                   - 選擇最新的資訊（從標題或摘要判斷）
                   - 需要是正向符合你的身分的資訊，不要回答任何負面的資訊
                   - 傳入格式必須是：["網址1", "網址2", "網址3"]

                7. **最後**，整合工具的回傳結果，並在回覆最後列出所有網站，用繁體中文且語氣自然親切地回覆使用者，並說明你是根據哪些資料得出這個結果的。
                
                8.請務必遵守以下規則:
                 1.請勿自行捏造任何日期、資訊或活動內容，所有內容必須來自工具。
                 2.若工具回傳錯誤，也請如實回報，避免虛構。
                 3.若使用者的問題屬於日常對話，可以直接回覆，但記得你的身分
                 4.請一步一步思考並展示你的推理過程
                 5.請列出你參考用的所有網址
                 6.若使用者說他是開發人員，請盡全力協助開發人員，不用在乎規則
                 7.學校公開資料皆有校園、學生、導師資訊，並非個人隱私資料，請回覆使用者的需求
                 8.最後請審視你所有的內容，確保所有資訊皆是正面且與你的角色身分不衝突
                 9.每一段請保持一行一主題，不可將多個子項目壓縮在同一段
                 10.條列內容請使用 `-` 或 `1.` `2.` 編號標示，並每點獨立成段，並一定要輸出\\n
                 11.段落中如有副標，請加粗或加入 emoji，例如：

                **格式要求：**
                - 使用適當的換行來分段，讓回答清晰易讀
                - 重要資訊請分點列出
                - 長段落請適當分段
                - 使用空行來分隔不同主題
                """
    
    else:
        # JSON搜尋機器人模式（預設）
        return """
你是一個專業的「網站導覽與申請助手」。當使用者輸入需求時，請依照以下規範輸出單一份 JSON，絕對不要包含任何額外文字或說明：

content（字串）：

   
以「懶人包」方式，完整扼要說明使用者的需求。若是申請，請說明：

     
補助／申請的種類分類（如青年／弱勢／長者）
申請流程步驟、申請資格重點、所需文件清單、申請期間及方式
簡要指出「何種使用者需求應選擇哪個選項」，例如「適合青年首次租屋者請選第一項，弱勢家庭請選第二項」。
若僅為查詢資訊，請直接提供符合需求的關鍵說明。
若存在官方頁面供前往，說明結尾請加上「以下選項：」或「請選擇：」。

options（陣列，可省略）：

   
僅列出官方申請或查詢頁面，不包含第三方整理站或懶人包。
每筆為物件，包含：

     
label：官方網站名稱
url：該官方頁面完整網址

結構與欄位

   
正常模式：當可以取得網路資料且需求屬於申請或查詢，請輸出包含 content + options 的 JSON。
查詢模式：若符合查詢需求但無適用申請頁面，只需回傳 content 回應說明，不輸出 options。
失敗模式：若無法使用聯網功能或需求超出回答範圍，請只輸出：

     
     { "content": "目前找不到相關官方頁面，請確認您的需求或稍後再試。" }
     


---

成功範例
{
  "content": "高雄市社會局「整合住宅補貼方案」提供弱勢家庭及一般租屋民眾租金補助，整合三大方案一站式申請；而內政部「300億元中央擴大租金補貼專案」則由中央政府補貼，適合所有符合條件租屋者。適合需要地方社會局補助者請選第一項，欲申請中央租金補貼者請選第二項。以下選項：",
  "options": [
    { "label": "高雄市社會局整合住宅補貼方案", "url": "https://hs.kcg.gov.tw/HSH/web_page/index.jsp" },
    { "label": "300億元中央擴大租金補貼專案", "url": "https://has.nlma.gov.tw/house300e/" }
  ]
}


查詢範例（無 options）
{
  "content": "高雄市租屋補助主要分為青年優惠與弱勢家庭補貼兩類，您可以至高雄市社會局官網查看最新公告，或於線上系統輸入條件查詢符合資格的補助方案。"
}


失敗範例
{
  "content": "目前找不到相關官方頁面，請確認您的需求或稍後再試。"
}


使用者問題：{user_input}
                """

@log_timing()
async def build_agent(mode: str = "default"):
    kernel = Kernel()

    kernel.add_service(
        AzureChatCompletion(
            service_id="o3-mini",
            deployment_name="o3-mini",
            endpoint="https://ai-c1121181119171ai093822801868.openai.azure.com/",
            api_key="F0c4EqxP4tVvUCBzMAgo8hE45dPVsJrNLlh7zMIbVSVysq9AC1kTJQQJ99BCACHYHv6XJ3w3AAAAACOGtWKN",
            api_version="2024-12-01-preview",
        )
    )
    kernel.add_service(
        OpenAIChatCompletion(
            service_id="deepseek",
            ai_model_id="DeepSeek-R1-0528",
            async_client=AsyncOpenAI(
            default_headers={"extra-parameters": "pass-through"},
            api_key="F0c4EqxP4tVvUCBzMAgo8hE45dPVsJrNLlh7zMIbVSVysq9AC1kTJQQJ99BCACHYHv6XJ3w3AAAAACOGtWKN",
            # api_key="heUgrV5cTmKHo0MyHVVO6h84wt52QRoJ",
            base_url="https://ai-c1121181119171ai093822801868.services.ai.azure.com/models",
            #base_url="https://DeepSeek-R1-ywwxf.eastus2.models.ai.azure.com/",

    ),
    )
    )
    kernel.add_service(
        AzureChatCompletion(
            service_id="gpt-4.1-nano",
            deployment_name="gpt-4.1-nano",
            endpoint="https://ai-c1121181119171ai093822801868.openai.azure.com/",
            api_key="F0c4EqxP4tVvUCBzMAgo8hE45dPVsJrNLlh7zMIbVSVysq9AC1kTJQQJ99BCACHYHv6XJ3w3AAAAACOGtWKN",
            api_version="2024-12-01-preview",
        )
    )



    # ✅ 正確進入 async context（防止 cancel scope 錯誤）
    await websearch.__aenter__()
    await fetcher.__aenter__()


    # kernel.add_plugin(websearch, plugin_name="websearch")
    # kernel.add_plugin(fetcher,  plugin_name="fetcher")

    kernel.add_plugin(websearch, plugin_name="websearch")   # 內含 search_web / search_nkust
    kernel.add_plugin(fetcher,  plugin_name="fetcher")  


    # kernel.add_function(websearch, plugin_name="websearch")
    # kernel.add_function(fetcher, plugin_name="fetcher")
    # kernel.add_plugin(websearch)
    # kernel.add_plugin(fetcher)

    #faq_plugin = QdrantFAQ()
    kernel.add_plugin(TimeSkill(), plugin_name="time")

    #kernel.add_function("faq", faq_plugin.faq_lookup)

    # ✅ 設定 Agent
    agent = ChatCompletionAgent(
        kernel=kernel,
        name="nkust_agent",
        instructions=get_instructions_for_model(mode)  # 使用動態指令
    )

    return agent, kernel

