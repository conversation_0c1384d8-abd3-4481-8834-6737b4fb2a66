#!/usr/bin/env python3
"""
快速測試模型回應
"""

import asyncio
import aiohttp
import json

async def quick_test():
    """快速測試兩個模型"""
    
    base_url = "http://localhost:8088/ui/chat"
    
    tests = [
        {
            "name": "o3-mini 測試",
            "data": {
                "user_id": "test_o3",
                "message": "你好",
                "model_id": "o3-mini", 
                "mode": "default"
            }
        },
        {
            "name": "DeepSeek 測試", 
            "data": {
                "user_id": "test_deepseek",
                "message": "你好",
                "model_id": "deepseek",
                "mode": "deepseek"
            }
        }
    ]
    
    for test in tests:
        print(f"\n🧪 {test['name']}")
        print("=" * 40)
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(base_url, json=test['data']) as response:
                    print(f"狀態: {response.status}")
                    
                    if response.status == 200:
                        content = ""
                        async for chunk in response.content.iter_chunked(1024):
                            if chunk:
                                text = chunk.decode('utf-8')
                                content += text
                                print(text, end='', flush=True)
                        
                        print(f"\n長度: {len(content)} 字元")
                        if len(content) == 0:
                            print("❌ 空回應!")
                        else:
                            print("✅ 有回應")
                    else:
                        error = await response.text()
                        print(f"❌ 錯誤: {error}")
                        
        except Exception as e:
            print(f"❌ 異常: {e}")

if __name__ == "__main__":
    asyncio.run(quick_test())
