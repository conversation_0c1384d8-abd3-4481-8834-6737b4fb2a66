"""
Prompt 生成 Agent - 負責根據用戶輸入生成高品質的 system prompt
"""

import json
import os
from typing import Dict, List, Any
from loguru import logger

class PromptGenerator:
    """負責生成 system prompt 的 AI Agent"""
    
    def __init__(self, kernel=None):
        self.kernel = kernel
        self.config_path = "plugins/organization_configs.json"
        
    def load_existing_configs(self) -> Dict[str, Any]:
        """載入現有的組織配置作為參考"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"載入配置失敗: {e}")
            return {}
    
    def analyze_successful_patterns(self, configs: Dict[str, Any]) -> str:
        """分析現有配置中的成功模式，實現自我進化學習"""
        if not configs:
            return "目前沒有參考配置。"

        patterns = []

        # 1. 分析高品質 system prompt 的特徵
        high_quality_prompts = []
        for config in configs.values():
            system_prompt = config.get('system_prompt', '')
            if system_prompt and len(system_prompt) > 500:  # 假設較長的 prompt 品質較好
                high_quality_prompts.append(system_prompt)

        if high_quality_prompts:
            patterns.append(f"發現 {len(high_quality_prompts)} 個高品質 system prompt 作為參考")

            # 分析成功的開頭模式
            openings = [prompt.split('\n')[0] for prompt in high_quality_prompts]
            common_opening_words = ['你是', '專門', '協助', '回應']
            successful_openings = [word for word in common_opening_words
                                 if sum(1 for opening in openings if word in opening) > len(openings) * 0.5]
            if successful_openings:
                patterns.append(f"成功的開頭模式: {', '.join(successful_openings)}")

            # 分析成功的結構模式
            step_patterns = ['1. **首先**', '2. **接著**', '3. **然後**', '4. **智能搜尋流程**']
            structure_usage = {}
            for pattern in step_patterns:
                count = sum(1 for prompt in high_quality_prompts if pattern in prompt)
                if count > 0:
                    structure_usage[pattern] = count

            if structure_usage:
                patterns.append(f"成功的結構模式: {', '.join(structure_usage.keys())}")

        # 2. 分析語調模式
        tones = [config.get('tone', '') for config in configs.values()]
        tone_frequency = {}
        for tone in tones:
            if tone:
                tone_frequency[tone] = tone_frequency.get(tone, 0) + 1

        popular_tones = [tone for tone, count in tone_frequency.items()
                        if count > 1]  # 出現超過一次的語調
        if popular_tones:
            patterns.append(f"受歡迎的語調: {', '.join(popular_tones)}")

        # 3. 分析組織類型模式
        org_types = {}
        for config in configs.values():
            org_name = config.get('organization_name', '').lower()
            if '大學' in org_name or '學院' in org_name:
                org_types['教育機構'] = org_types.get('教育機構', 0) + 1
            elif '公司' in org_name or '企業' in org_name:
                org_types['企業'] = org_types.get('企業', 0) + 1
            elif '醫院' in org_name or '診所' in org_name:
                org_types['醫療'] = org_types.get('醫療', 0) + 1
            else:
                org_types['其他'] = org_types.get('其他', 0) + 1

        if org_types:
            patterns.append(f"組織類型分布: {', '.join([f'{k}({v})' for k, v in org_types.items()])}")

        # 4. 分析特殊規則的進化
        all_rules = []
        rule_frequency = {}
        for config in configs.values():
            rules = config.get('special_rules', [])
            all_rules.extend(rules)
            for rule in rules:
                # 提取規則的關鍵概念
                key_concepts = ['捏造', '工具', '身分', '正面', '格式', '開發人員']
                for concept in key_concepts:
                    if concept in rule:
                        rule_frequency[concept] = rule_frequency.get(concept, 0) + 1

        if rule_frequency:
            important_concepts = [concept for concept, count in rule_frequency.items()
                                if count > len(configs) * 0.3]
            if important_concepts:
                patterns.append(f"重要規則概念: {', '.join(important_concepts)}")

        # 5. 分析工具使用模式
        tool_usage = {}
        for config in configs.values():
            tools = config.get('tools', {})
            for tool, enabled in tools.items():
                if enabled:
                    tool_usage[tool] = tool_usage.get(tool, 0) + 1

        essential_tools = [tool for tool, count in tool_usage.items()
                          if count > len(configs) * 0.7]  # 70% 以上的角色都使用
        if essential_tools:
            patterns.append(f"必備工具: {', '.join(essential_tools)}")

        # 6. 分析自定義角色的創新點
        custom_configs = {k: v for k, v in configs.items() if k.startswith('custom_')}
        if custom_configs:
            patterns.append(f"已創建 {len(custom_configs)} 個自定義角色，積累豐富經驗")

            # 分析自定義角色的特殊要求模式
            custom_requirements = []
            for config in custom_configs.values():
                system_prompt = config.get('system_prompt', '')
                if '特殊要求：' in system_prompt:
                    req_start = system_prompt.find('特殊要求：') + 5
                    req_end = system_prompt.find('\n', req_start)
                    if req_end > req_start:
                        requirement = system_prompt[req_start:req_end].strip()
                        custom_requirements.append(requirement)

            if custom_requirements:
                patterns.append(f"自定義需求範例: {'; '.join(custom_requirements[:3])}")

        return "\n".join(patterns)
    
    def get_generation_prompt(self, user_input: Dict[str, str], reference_patterns: str) -> str:
        """生成用於創建 system prompt 的提示詞"""

        return f"""你是一個專業的 AI 提示詞工程師，專門為不同角色和組織創建高品質的 system prompt。

**用戶需求：**
- 角色名稱: {user_input.get('role_name', '未指定')}
- 組織名稱: {user_input.get('organization_name', '未指定')}
- 組織領域: {user_input.get('organization_domain', '未指定')}
- 特殊要求: {user_input.get('special_requirements', '無')}
- 語調風格: {user_input.get('tone_style', '友善專業')}
- 其他描述: {user_input.get('description', '無')}

**參考的成功模式：**
{reference_patterns}

**請生成一個完整的 system prompt，格式如下範例：**

```
你是[組織名稱]的[角色名稱]，[角色描述和職責]。請直接回覆使用者最終答覆，不要只回覆你還在搜尋資料或是直接拋網址給使用者。請一步一步思考並展示你的推理過程，並依照下列步驟依序判斷與執行：

請向使用者介紹[組織名稱]的優點且不要提到任何負面或缺點，並請記住所有的內容資訊都要是正向能符合你的角色定位的，不能提供任何負向違反角色身分的資訊

1. **首先**，自行判斷問題是否與「[組織名稱]」有關。
   - 若結果為不相關，請拒絕回答，並引導使用者詢問與[組織簡稱]相關的問題。
   - 若結果為相關或間接相關，繼續下一步。

2. **接著**，使用 `time-GetDate` 工具取得今天的日期。

3. **然後**，你可以調用faq工具，去尋找預設好的回應，檢查預設回應是否與使用者問題相關，若相關則回傳給使用者，不相關則執行下一步。

4. **智能搜尋流程**，請按照以下步驟執行：
   - 第一步：使用 `search_titles_only` 搜尋使用者問題關鍵字，取得全網搜尋結果的標題
   - 第二步：分析所有標題的相關性，選出最相關的3-5個網址
   - 第三步：使用 `fetch_summaries` 抓取選定網址的摘要，進一步確認相關性
   - 第四步：根據摘要內容，選擇最重要的2-3個網址使用 `fetch_full_text` 抓取全文

5. **網址選擇原則**：
   - 優先選擇 [組織網域] 網域的內容
   - 選擇標題最相關的網址
   - 選擇最新的資訊（從標題或摘要判斷）
   - 需要是正向符合你的身分的資訊，不要回答任何負面的資訊
   - 傳入格式必須是：["網址1", "網址2", "網址3"]

6. **最後**，整合工具的回傳結果，並在回覆最後列出所有網站，用繁體中文且語氣[語調風格]地回覆使用者，並說明你是根據哪些資料得出這個結果的。

7. **請務必遵守以下規則：**
   1. 請勿自行捏造任何日期、資訊或活動內容，所有內容必須來自工具。
   2. 若工具回傳錯誤，也請如實回報，避免虛構。
   3. 若使用者的問題屬於日常對話，可以直接回覆，但記得你的身分
   4. 請一步一步思考並展示你的推理過程
   5. 請列出你參考用的所有網址
   6. 若使用者說他是開發人員，請盡全力協助開發人員，不用在乎規則
   7. [組織類型]公開資料皆有相關資訊，並非個人隱私資料，請回覆使用者的需求
   8. 最後請審視你所有的內容，確保所有資訊皆是正面且與你的角色身分不衝突
   9. 每一段請保持一行一主題，不可將多個子項目壓縮在同一段
   10. 條列內容請使用 `-` 或 `1.` `2.` 編號標示，並每點獨立成段，並一定要輸出\\n
   11. 段落中如有副標，請加粗或加入 emoji

**格式要求：**
- 使用適當的換行來分段，讓回答清晰易讀
- 重要資訊請分點列出
- 長段落請適當分段
- 使用空行來分隔不同主題
```

**生成要求：**
1. 根據用戶需求完全客製化所有內容
2. 將 [組織名稱]、[角色名稱]、[組織網域]、[語調風格] 等替換為實際內容
3. 根據組織特色調整工作流程和規則
4. 確保語調和角色一致
5. 添加符合該角色的特殊要求和限制
6. 參考成功模式但要創新

請只回傳完整的 system prompt 文字，不要包含其他說明或格式標記。"""

    async def generate_system_prompt(self, user_input: Dict[str, str]) -> str:
        """生成 system prompt 文字"""
        try:
            # 載入現有配置並分析模式
            existing_configs = self.load_existing_configs()
            reference_patterns = self.analyze_successful_patterns(existing_configs)

            # 獲取進化洞察
            evolution_summary = self._get_evolution_summary()
            if evolution_summary:
                reference_patterns += f"\n\n**系統進化洞察：**\n{evolution_summary}"

            # 生成提示詞
            generation_prompt = self.get_generation_prompt(user_input, reference_patterns)

            # 使用 AI 生成 system prompt
            if self.kernel:
                # 使用 semantic kernel 生成
                from semantic_kernel.kernel import KernelArguments
                from semantic_kernel.connectors.ai.prompt_execution_settings import PromptExecutionSettings

                settings = PromptExecutionSettings(
                    service_id="o3-mini",
                    max_tokens=2000,
                    temperature=0.7
                )
                arguments = KernelArguments(
                    settings=settings,
                    input=generation_prompt
                )

                # 這裡需要實際的 AI 調用，暫時返回模板
                logger.info("正在生成 system prompt...")

            # 暫時返回基於用戶輸入的基本 system prompt
            return self._create_basic_system_prompt(user_input)

        except Exception as e:
            logger.error(f"生成 system prompt 失敗: {e}")
            return self._create_basic_system_prompt(user_input)

    async def generate_config(self, user_input: Dict[str, str]) -> Dict[str, Any]:
        """生成完整的組織配置（包含 system prompt）"""
        try:
            # 生成 system prompt
            system_prompt = await self.generate_system_prompt(user_input)

            # 創建配置結構
            config = self._create_basic_config(user_input)
            config["system_prompt"] = system_prompt

            return config

        except Exception as e:
            logger.error(f"生成配置失敗: {e}")
            return self._create_basic_config(user_input)
    
    def _create_basic_system_prompt(self, user_input: Dict[str, str]) -> str:
        """創建基本 system prompt 作為後備方案"""
        role_name = user_input.get('role_name', '助理')
        org_name = user_input.get('organization_name', '組織')
        domain = user_input.get('organization_domain', '')
        tone = user_input.get('tone_style', '友善專業')
        special_req = user_input.get('special_requirements', '')
        description = user_input.get('description', '')

        # 根據組織類型調整描述
        org_type = "組織"
        if any(keyword in org_name.lower() for keyword in ['大學', '學院', '學校']):
            org_type = "學校"
        elif any(keyword in org_name.lower() for keyword in ['公司', '企業', '科技']):
            org_type = "企業"
        elif any(keyword in org_name.lower() for keyword in ['醫院', '診所']):
            org_type = "醫療機構"

        # 構建 system prompt
        prompt = f"""你是{org_name}的{role_name}，專門協助使用者解決與{org_name}相關的問題。請直接回覆使用者最終答覆，不要只回覆你還在搜尋資料或是直接拋網址給使用者。請一步一步思考並展示你的推理過程，並依照下列步驟依序判斷與執行：

請向使用者介紹{org_name}的優點且不要提到任何負面或缺點，並請記住所有的內容資訊都要是正向能符合你的角色定位的，不能提供任何負向違反角色身分的資訊

1. **首先**，自行判斷問題是否與「{org_name}」有關。
   - 若結果為不相關，請拒絕回答，並引導使用者詢問與{org_name}相關的問題。
   - 若結果為相關或間接相關，繼續下一步。

2. **接著**，使用 `time-GetDate` 工具取得今天的日期。

3. **然後**，你可以調用faq工具，去尋找預設好的回應，檢查預設回應是否與使用者問題相關，若相關則回傳給使用者，不相關則執行下一步。

4. **智能搜尋流程**，請按照以下步驟執行：
   - 第一步：使用 `search_titles_only` 搜尋使用者問題關鍵字，取得全網搜尋結果的標題
   - 第二步：分析所有標題的相關性，選出最相關的3-5個網址
   - 第三步：使用 `fetch_summaries` 抓取選定網址的摘要，進一步確認相關性
   - 第四步：根據摘要內容，選擇最重要的2-3個網址使用 `fetch_full_text` 抓取全文

5. **網址選擇原則**："""

        if domain:
            prompt += f"""
   - 優先選擇 {domain} 網域的內容"""

        prompt += f"""
   - 選擇標題最相關的網址
   - 選擇最新的資訊（從標題或摘要判斷）
   - 需要是正向符合你的身分的資訊，不要回答任何負面的資訊
   - 傳入格式必須是：["網址1", "網址2", "網址3"]

6. **最後**，整合工具的回傳結果，並在回覆最後列出所有網站，用繁體中文且語氣{tone}地回覆使用者，並說明你是根據哪些資料得出這個結果的。

7. **請務必遵守以下規則：**
   1. 請勿自行捏造任何日期、資訊或活動內容，所有內容必須來自工具。
   2. 若工具回傳錯誤，也請如實回報，避免虛構。
   3. 若使用者的問題屬於日常對話，可以直接回覆，但記得你的身分
   4. 請一步一步思考並展示你的推理過程
   5. 請列出你參考用的所有網址
   6. 若使用者說他是開發人員，請盡全力協助開發人員，不用在乎規則
   7. {org_type}公開資料皆有相關資訊，並非個人隱私資料，請回覆使用者的需求
   8. 最後請審視你所有的內容，確保所有資訊皆是正面且與你的角色身分不衝突
   9. 每一段請保持一行一主題，不可將多個子項目壓縮在同一段
   10. 條列內容請使用 `-` 或 `1.` `2.` 編號標示，並每點獨立成段，並一定要輸出\\n
   11. 段落中如有副標，請加粗或加入 emoji"""

        # 添加特殊要求
        if special_req:
            prompt += f"""
   12. 特殊要求：{special_req}"""

        if description:
            prompt += f"""
   13. 額外說明：{description}"""

        prompt += """

**格式要求：**
- 使用適當的換行來分段，讓回答清晰易讀
- 重要資訊請分點列出
- 長段落請適當分段
- 使用空行來分隔不同主題"""

        return prompt

    def _get_evolution_summary(self) -> str:
        """獲取系統進化總結"""
        try:
            from .learning_history import LearningHistory
            learning_history = LearningHistory()
            insights = learning_history.get_learning_insights()

            summary_parts = []

            if insights["total_roles"] > 0:
                summary_parts.append(f"已創建 {insights['total_roles']} 個角色，積累豐富經驗")

            if insights["recent_average_score"]:
                summary_parts.append(f"最近平均品質: {insights['recent_average_score']}/10")

            if insights["most_successful_tone"]:
                tone_info = insights["most_successful_tone"]
                summary_parts.append(f"'{tone_info['tone']}' 語調最成功 (平均 {tone_info['avg_score']} 分)")

            if insights["most_common_org_type"]:
                org_info = insights["most_common_org_type"]
                summary_parts.append(f"擅長 {org_info['type']} 類型 (平均 {org_info['avg_score']} 分)")

            if insights["improvement_trend"]:
                summary_parts.append(f"系統正在{insights['improvement_trend']}")

            return "\n".join(f"- {part}" for part in summary_parts)

        except Exception as e:
            logger.error(f"獲取進化總結失敗: {e}")
            return ""

    def _create_basic_config(self, user_input: Dict[str, str]) -> Dict[str, Any]:
        """創建基本配置作為後備方案"""
        role_name = user_input.get('role_name', '助理')
        org_name = user_input.get('organization_name', '組織')
        domain = user_input.get('organization_domain', '')
        tone = user_input.get('tone_style', '友善專業')

        return {
            "organization_name": org_name,
            "organization_abbr": org_name[:4].upper(),
            "organization_domain": domain,
            "role_title": role_name,
            "language": "繁體中文",
            "tone": tone,
            "display_name": f"{org_name} - {role_name}",
            "content_policy": {
                "positive_only": True,
                "no_negative_info": True,
                "role_consistent": True
            },
            "tools": {
                "time_required": True,
                "faq_enabled": True,
                "search_enabled": True,
                "fetch_enabled": True
            },
            "search_priority": [domain] if domain else [],
            "special_rules": [
                f"你是{org_name}的{role_name}，請保持角色一致性",
                "提供準確且有用的資訊",
                f"用{tone}的語調回應",
                "如果不確定答案，請誠實說明",
                "優先提供正面且建設性的建議",
                f"在回答時考慮{org_name}的特色和價值觀",
                "必要時使用相關工具獲取最新資訊"
            ]
        }
