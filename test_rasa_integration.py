#!/usr/bin/env python3
"""
測試 Rasa 整合到主對話框的功能
"""

import asyncio
import aiohttp
import json

async def test_rasa_integration():
    """測試 Rasa 整合功能"""
    
    base_url = "http://localhost:8088"
    
    # 測試案例
    test_cases = [
        {
            "name": "簡單問候 (應該由 Rasa 處理)",
            "message": "你好",
            "expected_source": "rasa"
        },
        {
            "name": "複雜問題 (應該轉交 AI)",
            "message": "今天有什麼活動嗎？請詳細說明",
            "expected_source": "ai"
        },
        {
            "name": "高科大相關問題 (可能 Rasa 或 AI)",
            "message": "高科大的課程有哪些？",
            "expected_source": "either"
        }
    ]
    
    print("🚀 開始測試 Rasa 整合功能")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 測試 {i}: {test_case['name']}")
        print("-" * 40)
        print(f"📤 問題: {test_case['message']}")
        
        try:
            # 發送請求到整合後的聊天端點
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{base_url}/ui/chat", json={
                    "user_id": f"test_user_{i}",
                    "message": test_case['message'],
                    "model_id": "o3-mini",
                    "mode": "default"
                }) as response:
                    
                    print(f"📊 狀態碼: {response.status}")
                    
                    if response.status == 200:
                        print("📥 回應內容:")
                        print("-" * 30)
                        
                        content = ""
                        async for chunk in response.content.iter_chunked(1024):
                            if chunk:
                                text = chunk.decode('utf-8')
                                content += text
                                print(text, end='', flush=True)
                        
                        print(f"\n-" * 30)
                        print(f"📏 總長度: {len(content)} 字元")
                        
                        # 分析回應來源
                        if "🤖 Rasa 智能助理回應" in content:
                            actual_source = "rasa"
                            print("✅ 由 Rasa 處理")
                        elif "🧠" in content and ("深度分析" in content or "思考中" in content):
                            actual_source = "ai"
                            print("✅ 由 AI Agent 處理")
                        elif "🔄" in content and "轉交" in content:
                            actual_source = "ai_fallback"
                            print("✅ Rasa 轉交給 AI Agent")
                        else:
                            actual_source = "unknown"
                            print("⚠️ 無法識別處理來源")
                        
                        # 驗證預期結果
                        expected = test_case['expected_source']
                        if expected == "either" or expected == actual_source or \
                           (expected == "ai" and actual_source == "ai_fallback"):
                            print("🎉 測試通過！")
                        else:
                            print(f"❌ 測試失敗：期望 {expected}，實際 {actual_source}")
                    
                    else:
                        error_text = await response.text()
                        print(f"❌ HTTP 錯誤 {response.status}: {error_text}")
                        
        except Exception as e:
            print(f"❌ 連接錯誤: {e}")
        
        print("\n" + "=" * 60)

async def test_rasa_direct():
    """直接測試 Rasa 服務"""
    print("\n🤖 直接測試 Rasa 服務")
    print("=" * 60)
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post("https://b225.54ucl.com/ui/rasa", json={
                "message": "你好",
                "user_id": "test_direct",
                "model_id": "rasa"
            }) as response:
                
                print(f"📊 Rasa 服務狀態: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    print("📥 Rasa 回應:")
                    print(json.dumps(data, ensure_ascii=False, indent=2))
                    
                    if isinstance(data, list) and len(data) > 0:
                        first_response = data[0]
                        confidence = first_response.get('confidence', 0)
                        print(f"⭐ 信心度: {confidence}")
                        
                        if confidence >= 0.8:
                            print("✅ Rasa 信心度足夠，應該直接使用")
                        else:
                            print("⚠️ Rasa 信心度不足，應該轉交 AI")
                    else:
                        print("❌ Rasa 回應格式異常")
                else:
                    error_text = await response.text()
                    print(f"❌ Rasa 服務錯誤: {error_text}")
                    
    except Exception as e:
        print(f"❌ Rasa 服務連接失敗: {e}")

async def main():
    """主測試函數"""
    print("🎯 Rasa 整合測試")
    print("=" * 80)
    
    # 1. 測試 Rasa 服務本身
    await test_rasa_direct()
    
    # 2. 測試整合後的聊天功能
    await test_rasa_integration()
    
    print("\n🎉 測試完成！")
    print("\n💡 預期行為:")
    print("- 簡單問候應該由 Rasa 快速回應")
    print("- 複雜問題應該轉交 AI Agent 深度處理")
    print("- 對話框會顯示處理來源和轉交過程")

if __name__ == "__main__":
    asyncio.run(main())
