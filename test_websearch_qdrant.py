#!/usr/bin/env python3
"""
測試 websearch tool 的 Qdrant 整合功能
"""

import asyncio
import sys
import os

# 添加 plugins 目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'plugins'))

from websearchtool import init_qdrant, save_to_qdrant, search_cached_content, vector_store
from qdrant_client import QdrantClient

async def test_qdrant_integration():
    """測試 Qdrant 整合功能"""
    print("🧪 測試 WebSearch Qdrant 整合")
    print("=" * 60)
    
    # 1. 測試初始化
    print("\n1️⃣ 測試 Qdrant 初始化")
    init_success = init_qdrant()
    
    if not init_success:
        print("❌ Qdrant 初始化失敗，請檢查：")
        print("   - Qdrant 服務是否運行 (http://localhost:6333)")
        print("   - 相關套件是否安裝 (langchain-qdrant, langchain-openai)")
        return False
    
    print("✅ Qdrant 初始化成功")
    
    # 2. 測試保存功能
    print("\n2️⃣ 測試保存功能")
    test_data = [
        {
            "url": "https://example.com/test1",
            "title": "測試文章 1",
            "content": "這是一篇關於人工智慧的測試文章，內容包含機器學習和深度學習的相關知識。",
            "content_type": "summary"
        },
        {
            "url": "https://example.com/test2", 
            "title": "測試文章 2",
            "content": "這是一篇關於高科大校園生活的文章，介紹了學校的各種設施和活動。",
            "content_type": "full_text"
        },
        {
            "url": "https://nkust.edu.tw/test3",
            "title": "高科大課程資訊",
            "content": "高雄科技大學提供多元化的課程，包括工程、管理、設計等各個領域的專業課程。",
            "content_type": "summary"
        }
    ]
    
    save_count = 0
    for data in test_data:
        success = await save_to_qdrant(
            data["url"], 
            data["title"], 
            data["content"], 
            data["content_type"]
        )
        if success:
            save_count += 1
            print(f"✅ 保存成功: {data['title']}")
        else:
            print(f"❌ 保存失敗: {data['title']}")
    
    print(f"\n📊 保存結果: {save_count}/{len(test_data)} 筆成功")
    
    # 3. 測試搜尋功能
    print("\n3️⃣ 測試搜尋功能")
    search_queries = [
        "人工智慧",
        "高科大",
        "課程資訊",
        "校園生活",
        "不存在的內容"
    ]
    
    for query in search_queries:
        print(f"\n🔍 搜尋: {query}")
        results = await search_cached_content(query, limit=3)
        
        if isinstance(results, list) and len(results) > 0:
            if "error" in results[0]:
                print(f"❌ 搜尋錯誤: {results[0]['error']}")
            elif "message" in results[0]:
                print(f"⚠️ {results[0]['message']}")
            else:
                print(f"✅ 找到 {len(results)} 筆結果:")
                for i, result in enumerate(results, 1):
                    print(f"   {i}. {result['title']} ({result['content_type']})")
                    print(f"      URL: {result['url']}")
                    print(f"      內容: {result['content'][:50]}...")
        else:
            print("❌ 搜尋返回異常結果")
    
    return True

async def test_collection_info():
    """測試集合資訊"""
    print("\n4️⃣ 檢查 Qdrant 集合資訊")
    
    try:
        client = QdrantClient(url="http://localhost:6333")
        
        # 列出所有集合
        collections = client.get_collections()
        print(f"📋 所有集合: {[c.name for c in collections.collections]}")
        
        # 檢查 websearch_cache 集合
        collection_name = "websearch_cache"
        try:
            info = client.get_collection(collection_name)
            print(f"📊 {collection_name} 集合資訊:")
            print(f"   - 點數量: {info.points_count}")
            print(f"   - 向量數量: {info.vectors_count}")
            print(f"   - 狀態: {info.status}")
        except Exception as e:
            print(f"⚠️ {collection_name} 集合不存在或無法訪問: {e}")
        
        # 檢查 faq 集合 (比較)
        try:
            faq_info = client.get_collection("faq")
            print(f"📊 faq 集合資訊 (比較):")
            print(f"   - 點數量: {faq_info.points_count}")
            print(f"   - 向量數量: {faq_info.vectors_count}")
        except Exception as e:
            print(f"⚠️ faq 集合不存在: {e}")
            
    except Exception as e:
        print(f"❌ 無法連接到 Qdrant: {e}")

async def main():
    """主測試函數"""
    print("🚀 WebSearch Qdrant 整合測試")
    print("=" * 80)
    
    # 測試整合功能
    success = await test_qdrant_integration()
    
    if success:
        # 檢查集合資訊
        await test_collection_info()
        
        print("\n🎉 測試完成！")
        print("\n💡 如果測試成功，您的 websearch tool 現在會：")
        print("   1. 自動將搜尋結果保存到 Qdrant")
        print("   2. 支援從快取中搜尋已保存的內容")
        print("   3. 與現有的 FAQ 系統共用相同的 embedding 模型")
        
        print("\n🔧 使用方法：")
        print("   - 正常使用 websearch 工具搜尋和抓取")
        print("   - 使用 search_cached_content 搜尋已快取的內容")
        print("   - 檢查 Qdrant 管理界面: http://localhost:6333/dashboard")
    else:
        print("\n❌ 測試失敗，請檢查配置和依賴")

if __name__ == "__main__":
    asyncio.run(main())
